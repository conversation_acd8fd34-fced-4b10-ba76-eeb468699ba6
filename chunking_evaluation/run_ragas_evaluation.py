#!/usr/bin/env python3
"""
RAGAS检索评估运行脚本

这个脚本使用RAGAS框架评估分块策略的检索质量，
集成到现有的项目结构中，使用已配置的阿里云API。
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.evaluation_framework.ragas_evaluation import RAGASRetrievalEvaluation
from chunking_evaluation.chunking import FixedTokenChunker, RecursiveTokenChunker
from chunking_evaluation.utils import get_openai_embedding_function

def check_ragas_installation():
    """检查RAGAS是否已安装"""
    try:
        import ragas
        print(f"✅ RAGAS已安装，版本: {ragas.__version__}")
        return True
    except ImportError:
        print("❌ RAGAS未安装")
        print("请运行: pip install ragas")
        return False

def run_single_chunker_evaluation():
    """运行单个分块器的RAGAS评估"""
    
    print("🎯 RAGAS检索评估 - 单个分块器测试")
    print("="*80)
    
    # 配置路径
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/ragas_test_db"
    
    # 检查数据集
    if not Path(questions_csv_path).exists():
        print(f"❌ 数据集文件不存在: {questions_csv_path}")
        return None
    
    # 创建评估器（模型名称留空）
    try:
        evaluator = RAGASRetrievalEvaluation(
            questions_csv_path=questions_csv_path,
            chroma_db_path=db_path,
            model_name=""  # 模型名称留空
        )
        print("✅ RAGAS评估器初始化成功")
        print(f"🤖 使用模型: {evaluator.model_name}")
    except Exception as e:
        print(f"❌ 评估器初始化失败: {e}")
        return None
    
    # 创建分块器（使用表现最好的配置）
    chunker = FixedTokenChunker(chunk_size=200, chunk_overlap=20)
    print(f"📦 分块器: {chunker.__class__.__name__}(chunk_size=200, chunk_overlap=20)")
    
    # 获取嵌入函数
    try:
        embedding_function = get_openai_embedding_function()
        print("✅ 嵌入函数获取成功")
    except Exception as e:
        print(f"❌ 嵌入函数获取失败: {e}")
        return None
    
    # 运行评估
    try:
        print(f"\n🚀 开始RAGAS评估...")
        result = evaluator.run_ragas_evaluation(
            chunker=chunker,
            embedding_function=embedding_function,
            retrieve=5,
            db_to_save_chunks=db_path,
            max_questions=3,  # 限制为前3个问题进行测试
            debug=True  # 开启调试模式
        )
        
        return result
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_multiple_chunkers_evaluation():
    """运行多个分块器的RAGAS评估"""
    
    print("🎯 RAGAS检索评估 - 多分块器对比")
    print("="*80)
    
    # 配置路径
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    base_db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data"
    
    # 定义要测试的分块器配置（简化版）
    chunker_configs = [
        {"class": FixedTokenChunker, "params": {"chunk_size": 200, "chunk_overlap": 20}},
        {"class": RecursiveTokenChunker, "params": {"chunk_size": 200, "chunk_overlap": 20}},
    ]
    
    # 获取嵌入函数
    embedding_function = get_openai_embedding_function()
    
    all_results = {}
    
    for i, config in enumerate(chunker_configs):
        chunker_class = config["class"]
        params = config["params"]
        
        print(f"\n📦 评估分块器 {i+1}/{len(chunker_configs)}: {chunker_class.__name__}")
        print(f"   参数: {params}")
        
        # 创建分块器
        chunker = chunker_class(**params)
        
        # 创建数据库路径
        db_path = f"{base_db_path}/ragas_db_{chunker_class.__name__}_{i}"
        
        # 创建评估器
        try:
            evaluator = RAGASRetrievalEvaluation(
                questions_csv_path=questions_csv_path,
                chroma_db_path=db_path,
                model_name=""  # 模型名称留空
            )
        except Exception as e:
            print(f"❌ 评估器创建失败: {e}")
            continue
        
        # 运行评估
        try:
            result = evaluator.run_ragas_evaluation(
                chunker=chunker,
                embedding_function=embedding_function,
                retrieve=5,
                db_to_save_chunks=db_path,
                max_questions=5,  # 评估前5个问题
                debug=False  # 关闭调试模式以减少输出
            )
            
            config_name = result['chunker_config']
            all_results[config_name] = result
            
            # 显示结果摘要
            ragas_metrics = result.get('ragas_metrics', {})
            print(f"   📊 RAGAS结果:")
            for metric_name, stats in ragas_metrics.items():
                if 'mean' in stats:
                    print(f"     {metric_name}: {stats['mean']:.3f} (成功率: {stats['success_rate']:.1%})")
            
            api_calls = result.get('api_calls_estimated', 0)
            print(f"   📞 API调用次数: {api_calls}")
            
        except Exception as e:
            print(f"❌ 评估失败 {chunker_class.__name__}: {e}")
    
    return all_results

def save_results(results: Dict, filename: str = "ragas_evaluation_results.json"):
    """保存评估结果"""
    
    output_dir = Path("chunking_evaluation/evaluation_framework/outputs")
    output_dir.mkdir(exist_ok=True)
    
    output_file = output_dir / filename
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 RAGAS结果已保存到: {output_file}")
    return output_file

def generate_comparison_report(ragas_results: Dict):
    """生成对比报告"""
    
    print(f"\n📊 RAGAS评估结果分析")
    print("="*80)
    
    if not ragas_results:
        print("❌ 没有结果可分析")
        return
    
    # 按分块器类型分组分析
    chunker_types = {}
    for config_name, result in ragas_results.items():
        if 'FixedTokenChunker' in config_name:
            chunker_type = 'FixedTokenChunker'
        elif 'RecursiveTokenChunker' in config_name:
            chunker_type = 'RecursiveTokenChunker'
        else:
            chunker_type = 'Unknown'
        
        if chunker_type not in chunker_types:
            chunker_types[chunker_type] = []
        
        chunker_types[chunker_type].append((config_name, result))
    
    # 分析每种分块器类型
    for chunker_type, configs in chunker_types.items():
        print(f"\n🔍 {chunker_type} 分析:")
        print(f"   配置数量: {len(configs)}")
        
        # 计算平均指标
        all_precision = []
        all_recall = []
        all_entity_recall = []
        
        for config_name, result in configs:
            metrics = result.get('ragas_metrics', {})
            
            if 'context_precision' in metrics and 'mean' in metrics['context_precision']:
                all_precision.append(metrics['context_precision']['mean'])
            
            if 'context_recall' in metrics and 'mean' in metrics['context_recall']:
                all_recall.append(metrics['context_recall']['mean'])
            
            if 'context_entity_recall' in metrics and 'mean' in metrics['context_entity_recall']:
                all_entity_recall.append(metrics['context_entity_recall']['mean'])
        
        if all_precision:
            print(f"   平均Context Precision: {sum(all_precision)/len(all_precision):.3f}")
        if all_recall:
            print(f"   平均Context Recall: {sum(all_recall)/len(all_recall):.3f}")
        if all_entity_recall:
            print(f"   平均Context Entity Recall: {sum(all_entity_recall)/len(all_entity_recall):.3f}")
        
        # 显示最佳配置
        if configs:
            best_config = max(configs, key=lambda x: x[1].get('ragas_metrics', {}).get('context_precision', {}).get('mean', 0))
            print(f"   最佳配置: {best_config[0]}")

def compare_with_original_results(ragas_results: Dict):
    """与原始评估结果对比"""
    
    print(f"\n🔄 RAGAS vs 原始评估对比")
    print("="*80)
    
    # 读取原始结果
    original_file = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if not Path(original_file).exists():
        print(f"❌ 原始结果文件不存在: {original_file}")
        return
    
    with open(original_file, 'r', encoding='utf-8') as f:
        original_results = json.load(f)
    
    print(f"📋 对比分析:")
    
    for config_name, ragas_result in ragas_results.items():
        print(f"\n🔍 配置: {config_name}")
        
        # RAGAS结果
        ragas_metrics = ragas_result.get('ragas_metrics', {})
        print(f"   RAGAS指标:")
        for metric_name, stats in ragas_metrics.items():
            if 'mean' in stats:
                print(f"     {metric_name}: {stats['mean']:.3f}")
        
        # API调用信息
        api_calls = ragas_result.get('api_calls_estimated', 0)
        print(f"   API调用次数: {api_calls}")
        
        # 原始结果
        if config_name in original_results:
            original_data = original_results[config_name]
            print(f"   原始指标:")
            print(f"     precision_mean: {original_data.get('precision_mean', 0):.3f}")
            print(f"     recall_mean: {original_data.get('recall_mean', 0):.3f}")
            print(f"     iou_mean: {original_data.get('iou_mean', 0):.3f}")
        else:
            print(f"   ⚠️  未找到对应的原始结果")

def main():
    """主函数"""
    
    print("🎯 RAGAS检索评估系统")
    print("="*100)
    
    # 检查RAGAS安装
    if not check_ragas_installation():
        return
    
    print(f"\n📋 评估说明:")
    print(f"   - 使用项目中已配置的阿里云通义千问API")
    print(f"   - 模型名称参数留空，使用默认配置")
    print(f"   - 只评估检索指标，不涉及生成部分")
    print(f"   - 评估指标: Context Precision, Context Recall, Context Entity Recall")
    print(f"   - API调用次数比DeepEval少很多（约5-10次/问题 vs 300次/问题）")
    
    # 选择评估模式
    print(f"\n🔧 选择评估模式:")
    print(f"   1. 单个分块器测试 (快速验证)")
    print(f"   2. 多分块器对比 (完整评估)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        # 单个分块器测试
        result = run_single_chunker_evaluation()
        
        if result:
            # 保存结果
            save_results({result['chunker_config']: result}, "ragas_single_test.json")
            
            print(f"\n🎉 单个分块器测试完成!")
            print(f"📊 建议: 如果结果正常，可以运行完整的多分块器评估")
    
    elif choice == "2":
        # 多分块器评估
        results = run_multiple_chunkers_evaluation()
        
        if results:
            # 保存结果
            save_results(results, "ragas_multiple_chunkers.json")
            
            # 生成分析报告
            generate_comparison_report(results)
            
            # 与原始结果对比
            compare_with_original_results(results)
            
            print(f"\n🎉 多分块器评估完成!")
            print(f"📊 可以查看保存的结果文件进行详细分析")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
