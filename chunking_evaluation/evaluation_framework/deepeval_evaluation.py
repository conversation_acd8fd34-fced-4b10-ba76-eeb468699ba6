"""
DeepEval检索评估模块

这个模块集成DeepEval框架到现有的评估系统中，专门用于检索质量评估。
使用项目中已配置的阿里云通义千问API进行评估。
"""

import json
import pandas as pd
from typing import List, Dict, Any
from .base_evaluation import BaseEvaluation, DEEPEVAL_AVAILABLE, CustomLLMForDeepEval

if DEEPEVAL_AVAILABLE:
    from deepeval.metrics import (
        ContextualPrecisionMetric,
        ContextualRecallMetric,
        ContextualRelevancyMetric
    )
    from deepeval.test_case import LLMTestCase


class DeepEvalRetrievalEvaluation(BaseEvaluation):
    """
    基于DeepEval框架的检索评估类
    
    这个类继承自BaseEvaluation，专门用于使用DeepEval框架评估检索质量。
    它使用项目中已配置的阿里云通义千问API，只评估检索指标，不涉及生成部分。
    """
    
    def __init__(self, questions_csv_path: str, chroma_db_path=None):
        """
        初始化DeepEval检索评估类
        
        参数:
            questions_csv_path (str): 问题数据集CSV文件路径
            chroma_db_path (str, 可选): ChromaDB数据库路径
        """
        super().__init__(questions_csv_path, chroma_db_path)
        
        if not DEEPEVAL_AVAILABLE:
            raise ImportError("DeepEval未安装，请运行: pip install deepeval")
        
        # 使用项目中的API配置（与SyntheticEvaluationChinese相同）
        self.api_key = "sk-ab756c213e1248aea064b2e49ad24de8"
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        # 初始化自定义LLM
        self.custom_llm = CustomLLMForDeepEval(
            api_key=self.api_key,
            base_url=self.base_url,
            model="qwen-turbo"
        )
        
        # 初始化DeepEval指标（降低threshold进行调试）
        self.contextual_precision = ContextualPrecisionMetric(
            threshold=0.5,  # 降低threshold
            model=self.custom_llm,
            include_reason=True
        )

        self.contextual_recall = ContextualRecallMetric(
            threshold=0.5,  # 降低threshold
            model=self.custom_llm,
            include_reason=True
        )

        self.contextual_relevancy = ContextualRelevancyMetric(
            threshold=0.5,  # 降低threshold
            model=self.custom_llm,
            include_reason=True
        )
    
    def _convert_to_deepeval_format(self, question_data: Dict, retrieval_results: List[Dict]) -> LLMTestCase:
        """
        将项目数据格式转换为DeepEval格式
        
        参数:
            question_data: 问题数据（来自CSV）
            retrieval_results: 检索结果列表
            
        返回:
            LLMTestCase: DeepEval测试用例
        """
        # 提取问题文本
        input_text = question_data['question']
        
        # 提取期望答案（从references中合并所有content）
        references = question_data['references']
        if isinstance(references, str):
            references = json.loads(references)
        
        expected_output = " ".join([ref['content'] for ref in references])
        
        # 提取检索上下文
        retrieval_context = [
            result['document_content'] 
            for result in retrieval_results
        ]
        
        # 创建DeepEval测试用例
        test_case = LLMTestCase(
            input=input_text,
            expected_output=expected_output,
            actual_output="",  # 空字符串，因为我们只评估检索
            retrieval_context=retrieval_context
        )
        
        return test_case
    
    def _evaluate_single_question_deepeval(self, question_data: Dict, retrieval_results: List[Dict]) -> Dict:
        """
        使用DeepEval评估单个问题（带调试信息）

        参数:
            question_data: 问题数据
            retrieval_results: 检索结果

        返回:
            Dict: 评估结果
        """
        print(f"\n🔍 详细调试信息:")
        print(f"   问题: {question_data['question']}")

        # 显示期望答案
        references = question_data['references']
        if isinstance(references, str):
            references = json.loads(references)

        expected_content = " ".join([ref['content'] for ref in references])
        print(f"   期望答案: {expected_content[:200]}...")

        # 显示检索结果
        print(f"   检索到 {len(retrieval_results)} 个文档:")
        for i, result in enumerate(retrieval_results[:3]):  # 只显示前3个
            content = result['document_content']
            similarity = result.get('similarity_score', 0)
            print(f"     [{i+1}] 相似度: {similarity:.3f}")
            print(f"         内容: {content[:150]}...")

        # 转换为DeepEval格式
        test_case = self._convert_to_deepeval_format(question_data, retrieval_results)

        results = {}

        # 评估Contextual Precision（降低threshold）
        try:
            # 临时降低threshold进行测试
            self.contextual_precision.threshold = 0.5
            self.contextual_precision.measure(test_case)
            results['contextual_precision'] = {
                'score': self.contextual_precision.score,
                'success': self.contextual_precision.success,
                'reason': self.contextual_precision.reason
            }
            print(f"   ✅ Contextual Precision: {self.contextual_precision.score:.3f}")
        except Exception as e:
            print(f"   ❌ Contextual Precision评估失败: {e}")
            results['contextual_precision'] = {'error': str(e)}

        # 评估Contextual Recall
        try:
            self.contextual_recall.threshold = 0.5
            self.contextual_recall.measure(test_case)
            results['contextual_recall'] = {
                'score': self.contextual_recall.score,
                'success': self.contextual_recall.success,
                'reason': self.contextual_recall.reason
            }
            print(f"   ✅ Contextual Recall: {self.contextual_recall.score:.3f}")
        except Exception as e:
            print(f"   ❌ Contextual Recall评估失败: {e}")
            results['contextual_recall'] = {'error': str(e)}

        # 评估Contextual Relevancy
        try:
            self.contextual_relevancy.threshold = 0.5
            self.contextual_relevancy.measure(test_case)
            results['contextual_relevancy'] = {
                'score': self.contextual_relevancy.score,
                'success': self.contextual_relevancy.success,
                'reason': self.contextual_relevancy.reason
            }
            print(f"   ✅ Contextual Relevancy: {self.contextual_relevancy.score:.3f}")
        except Exception as e:
            print(f"   ❌ Contextual Relevancy评估失败: {e}")
            results['contextual_relevancy'] = {'error': str(e)}

        return results
    
    def run_deepeval_evaluation(self, chunker, embedding_function, retrieve=5, 
                               db_to_save_chunks=None, max_questions=None):
        """
        运行DeepEval检索评估
        
        参数:
            chunker: 分块器实例
            embedding_function: 嵌入函数
            retrieve (int): 检索的文档数量
            db_to_save_chunks (str, 可选): 保存分块的数据库路径
            max_questions (int, 可选): 最大评估问题数量
            
        返回:
            Dict: 评估结果
        """
        print("🔍 开始DeepEval检索评估...")
        print(f"📊 评估指标: Contextual Precision, Contextual Recall, Contextual Relevancy")
        
        # 使用父类的方法进行基础评估（获取检索结果）
        base_results = self.run(
            chunker=chunker,
            embedding_function=embedding_function,
            retrieve=retrieve,
            db_to_save_chunks=db_to_save_chunks,
            include_debug_info=True
        )
        
        # 获取问题和检索结果
        questions_with_results = base_results.get('debug_info', {}).get('questions', [])
        
        if max_questions:
            questions_with_results = questions_with_results[:max_questions]
        
        # 加载原始问题数据
        questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
        questions_df['references'] = questions_df['references'].apply(
            lambda x: json.loads(x) if isinstance(x, str) else x
        )
        
        deepeval_results = []
        
        for i, question_result in enumerate(questions_with_results):
            print(f"\n🔍 评估问题 {i+1}/{len(questions_with_results)}")
            
            question_text = question_result.get('question_text', '')
            corpus_id = question_result.get('corpus_id', '')
            retrieval_results = question_result.get('retrieval_results', [])
            
            # 在原始数据中找到对应问题
            matching_questions = questions_df[
                (questions_df['question'] == question_text) & 
                (questions_df['corpus_id'] == corpus_id)
            ]
            
            if len(matching_questions) == 0:
                print(f"⚠️  警告: 未找到匹配的问题")
                continue
            
            question_data = matching_questions.iloc[0].to_dict()
            
            # 使用DeepEval评估
            deepeval_metrics = self._evaluate_single_question_deepeval(
                question_data, retrieval_results
            )
            
            # 合并结果
            result = {
                'question_index': i,
                'question_text': question_text,
                'corpus_id': corpus_id,
                'deepeval_metrics': deepeval_metrics,
                'retrieval_results': retrieval_results
            }
            
            deepeval_results.append(result)
            
            # 显示结果
            for metric_name, metric_result in deepeval_metrics.items():
                if 'score' in metric_result:
                    score = metric_result['score']
                    success = "✅" if metric_result.get('success', False) else "❌"
                    print(f"   {metric_name}: {score:.3f} {success}")
        
        # 计算总体统计
        overall_stats = self._calculate_deepeval_stats(deepeval_results)
        
        # 构建分块器配置名称
        chunker_name = chunker.__class__.__name__
        if hasattr(chunker, 'chunk_size') and hasattr(chunker, 'chunk_overlap'):
            chunker_config = f"{chunker_name}_chunk_overlap{chunker.chunk_overlap}_chunk_size{chunker.chunk_size}"
        else:
            chunker_config = chunker_name

        # 构建最终结果
        final_result = {
            'chunker_config': chunker_config,
            'deepeval_metrics': overall_stats,
            'questions': deepeval_results,
            'total_questions': len(deepeval_results)
        }
        
        print(f"\n🎉 DeepEval评估完成!")
        print(f"📊 总体结果:")
        for metric_name, stats in overall_stats.items():
            if 'mean' in stats:
                print(f"   {metric_name}: {stats['mean']:.3f} (±{stats['std']:.3f})")
        
        return final_result
    
    def _calculate_deepeval_stats(self, results: List[Dict]) -> Dict:
        """计算DeepEval指标的统计信息"""
        stats = {}
        
        metrics = ['contextual_precision', 'contextual_recall', 'contextual_relevancy']
        
        for metric_name in metrics:
            scores = []
            success_count = 0
            
            for result in results:
                metric_result = result['deepeval_metrics'].get(metric_name, {})
                if 'score' in metric_result:
                    scores.append(metric_result['score'])
                    if metric_result.get('success', False):
                        success_count += 1
            
            if scores:
                stats[metric_name] = {
                    'mean': sum(scores) / len(scores),
                    'std': (sum((x - sum(scores)/len(scores))**2 for x in scores) / len(scores))**0.5,
                    'min': min(scores),
                    'max': max(scores),
                    'success_rate': success_count / len(scores)
                }
            else:
                stats[metric_name] = {'error': 'No valid scores'}
        
        return stats
