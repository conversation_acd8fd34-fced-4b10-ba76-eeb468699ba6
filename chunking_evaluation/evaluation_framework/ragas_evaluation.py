"""
RAGAS检索评估模块

这个模块集成RAGAS框架到现有的评估系统中，专门用于检索质量评估。
使用项目中已配置的阿里云通义千问API进行评估，专注于检索指标。
"""

import json
import pandas as pd
import asyncio
from typing import List, Dict, Any, Optional
from .base_evaluation import BaseEvaluation

# RAGAS相关导入（可选，如果未安装会跳过）
try:
    from ragas.dataset_schema import SingleTurnSample
    from ragas.metrics import (
        LLMContextPrecisionWithoutReference,
        LLMContextRecall,
        ContextEntityRecall
    )
    from ragas.llms import LangchainLLMWrapper
    from langchain_openai import ChatOpenAI
    RAGAS_AVAILABLE = True
except ImportError:
    RAGAS_AVAILABLE = False


def create_custom_llm_for_ragas(api_key: str, base_url: str, model: str = ""):
    """
    创建用于RAGAS的自定义LLM
    使用Langchain的ChatOpenAI包装器，配置阿里云API
    """
    if not RAGAS_AVAILABLE:
        raise ImportError("RAGAS未安装，请运行: pip install ragas")

    model_name = model  # 保持空白，不设置默认值

    # 使用Langchain的ChatOpenAI，配置为使用阿里云API
    langchain_llm = ChatOpenAI(
        model=model_name,
        openai_api_key=api_key,
        openai_api_base=base_url,
        temperature=0.1,
        max_tokens=1000,
        timeout=60  # 增加超时时间
    )

    # 包装为RAGAS兼容的LLM
    ragas_llm = LangchainLLMWrapper(langchain_llm)

    return ragas_llm


class RAGASRetrievalEvaluation(BaseEvaluation):
    """
    基于RAGAS框架的检索评估类
    
    这个类继承自BaseEvaluation，专门用于使用RAGAS框架评估检索质量。
    它使用项目中已配置的阿里云通义千问API，只评估检索指标。
    """
    
    def __init__(self, questions_csv_path: str, chroma_db_path=None, model_name: str = ""):
        """
        初始化RAGAS检索评估类
        
        参数:
            questions_csv_path (str): 问题数据集CSV文件路径
            chroma_db_path (str, 可选): ChromaDB数据库路径
            model_name (str): 模型名称，可以留空
        """
        super().__init__(questions_csv_path, chroma_db_path)
        
        if not RAGAS_AVAILABLE:
            raise ImportError("RAGAS未安装，请运行: pip install ragas")
        
        # 使用项目中的API配置（与SyntheticEvaluationChinese相同）
        self.api_key = "sk-ab756c213e1248aea064b2e49ad24de8"
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.model_name = model_name  # 保持空白，不设置默认值
        
        # 初始化自定义LLM
        self.custom_llm = create_custom_llm_for_ragas(
            api_key=self.api_key,
            base_url=self.base_url,
            model=self.model_name
        )
        
        # 初始化RAGAS检索指标
        self.context_precision = LLMContextPrecisionWithoutReference(llm=self.custom_llm)
        self.context_recall = LLMContextRecall(llm=self.custom_llm)
        self.context_entity_recall = ContextEntityRecall(llm=self.custom_llm)
        
        # 指标列表
        self.retrieval_metrics = [
            self.context_precision,
            self.context_recall,
            self.context_entity_recall
        ]
    
    def _convert_to_ragas_format(self, question_data: Dict, retrieval_results: List[Dict]) -> SingleTurnSample:
        """
        将项目数据格式转换为RAGAS格式
        
        参数:
            question_data: 问题数据（来自CSV）
            retrieval_results: 检索结果列表
            
        返回:
            SingleTurnSample: RAGAS测试样本
        """
        # 提取问题文本
        user_input = question_data['question']
        
        # 提取期望答案（从references中合并所有content）
        references = question_data['references']
        if isinstance(references, str):
            references = json.loads(references)
        
        reference = " ".join([ref['content'] for ref in references])
        
        # 提取检索上下文
        retrieved_contexts = [
            result['document_content'] 
            for result in retrieval_results
        ]
        
        # 创建RAGAS测试样本
        sample = SingleTurnSample(
            user_input=user_input,
            reference=reference,
            retrieved_contexts=retrieved_contexts
        )
        
        return sample
    
    def _evaluate_single_question_ragas(self, question_data: Dict, retrieval_results: List[Dict], 
                                       debug: bool = False) -> Dict:
        """
        使用RAGAS评估单个问题
        
        参数:
            question_data: 问题数据
            retrieval_results: 检索结果
            debug: 是否显示调试信息
            
        返回:
            Dict: 评估结果
        """
        if debug:
            print(f"\n🔍 RAGAS评估调试信息:")
            print(f"   问题: {question_data['question']}")
            
            # 显示期望答案
            references = question_data['references']
            if isinstance(references, str):
                references = json.loads(references)
            
            expected_content = " ".join([ref['content'] for ref in references])
            print(f"   期望答案: {expected_content[:200]}...")
            
            # 显示检索结果
            print(f"   检索到 {len(retrieval_results)} 个文档:")
            for i, result in enumerate(retrieval_results[:3]):  # 只显示前3个
                content = result['document_content']
                similarity = result.get('similarity_score', 0)
                print(f"     [{i+1}] 相似度: {similarity:.3f}")
                print(f"         内容: {content[:150]}...")
        
        # 转换为RAGAS格式
        sample = self._convert_to_ragas_format(question_data, retrieval_results)
        
        results = {}
        
        # 评估Context Precision
        try:
            precision_score = asyncio.run(self.context_precision.single_turn_ascore(sample))
            results['context_precision'] = {
                'score': precision_score,
                'success': True
            }
            if debug:
                print(f"   ✅ Context Precision: {precision_score:.3f}")
        except Exception as e:
            print(f"   ❌ Context Precision评估失败: {e}")
            results['context_precision'] = {'error': str(e), 'success': False}
        
        # 评估Context Recall
        try:
            recall_score = asyncio.run(self.context_recall.single_turn_ascore(sample))
            results['context_recall'] = {
                'score': recall_score,
                'success': True
            }
            if debug:
                print(f"   ✅ Context Recall: {recall_score:.3f}")
        except Exception as e:
            print(f"   ❌ Context Recall评估失败: {e}")
            results['context_recall'] = {'error': str(e), 'success': False}
        
        # 评估Context Entity Recall
        try:
            entity_recall_score = asyncio.run(self.context_entity_recall.single_turn_ascore(sample))
            results['context_entity_recall'] = {
                'score': entity_recall_score,
                'success': True
            }
            if debug:
                print(f"   ✅ Context Entity Recall: {entity_recall_score:.3f}")
        except Exception as e:
            print(f"   ❌ Context Entity Recall评估失败: {e}")
            results['context_entity_recall'] = {'error': str(e), 'success': False}
        
        return results

    def run_ragas_evaluation(self, chunker, embedding_function, retrieve=5,
                            db_to_save_chunks=None, max_questions=None, debug=False):
        """
        运行RAGAS检索评估

        参数:
            chunker: 分块器实例
            embedding_function: 嵌入函数
            retrieve (int): 检索的文档数量
            db_to_save_chunks (str, 可选): 保存分块的数据库路径
            max_questions (int, 可选): 最大评估问题数量
            debug (bool): 是否显示调试信息

        返回:
            Dict: 评估结果
        """
        print("🔍 开始RAGAS检索评估...")
        print(f"📊 评估指标: Context Precision, Context Recall, Context Entity Recall")
        print(f"🤖 使用模型: {self.model_name}")

        # 使用父类的方法进行基础评估（获取检索结果）
        base_results = self.run(
            chunker=chunker,
            embedding_function=embedding_function,
            retrieve=retrieve,
            db_to_save_chunks=db_to_save_chunks,
            include_debug_info=True
        )

        # 获取问题和检索结果
        questions_with_results = base_results.get('debug_info', {}).get('questions', [])

        if max_questions:
            questions_with_results = questions_with_results[:max_questions]

        # 加载原始问题数据
        questions_df = pd.read_csv(self.questions_csv_path, encoding='utf-8')
        questions_df['references'] = questions_df['references'].apply(
            lambda x: json.loads(x) if isinstance(x, str) else x
        )

        ragas_results = []
        api_call_count = 0

        for i, question_result in enumerate(questions_with_results):
            print(f"\n🔍 评估问题 {i+1}/{len(questions_with_results)}")

            question_text = question_result.get('question_text', '')
            corpus_id = question_result.get('corpus_id', '')
            retrieval_results = question_result.get('retrieval_results', [])

            # 在原始数据中找到对应问题
            matching_questions = questions_df[
                (questions_df['question'] == question_text) &
                (questions_df['corpus_id'] == corpus_id)
            ]

            if len(matching_questions) == 0:
                print(f"⚠️  警告: 未找到匹配的问题")
                continue

            question_data = matching_questions.iloc[0].to_dict()

            # 使用RAGAS评估
            ragas_metrics = self._evaluate_single_question_ragas(
                question_data, retrieval_results, debug=debug
            )

            # 估算API调用次数（每个指标约1-2次调用）
            api_call_count += len([m for m in ragas_metrics.values() if m.get('success', False)]) * 2

            # 合并结果
            result = {
                'question_index': i,
                'question_text': question_text,
                'corpus_id': corpus_id,
                'ragas_metrics': ragas_metrics,
                'retrieval_results': retrieval_results
            }

            ragas_results.append(result)

            # 显示结果
            for metric_name, metric_result in ragas_metrics.items():
                if 'score' in metric_result:
                    score = metric_result['score']
                    success = "✅" if metric_result.get('success', False) else "❌"
                    print(f"   {metric_name}: {score:.3f} {success}")

        # 计算总体统计
        overall_stats = self._calculate_ragas_stats(ragas_results)

        # 构建分块器配置名称
        chunker_name = chunker.__class__.__name__
        if hasattr(chunker, 'chunk_size') and hasattr(chunker, 'chunk_overlap'):
            chunker_config = f"{chunker_name}_chunk_overlap{chunker.chunk_overlap}_chunk_size{chunker.chunk_size}"
        else:
            chunker_config = chunker_name

        # 构建最终结果
        final_result = {
            'chunker_config': chunker_config,
            'ragas_metrics': overall_stats,
            'questions': ragas_results,
            'total_questions': len(ragas_results),
            'api_calls_estimated': api_call_count,
            'model_used': self.model_name
        }

        print(f"\n🎉 RAGAS评估完成!")
        print(f"📊 总体结果:")
        for metric_name, stats in overall_stats.items():
            if 'mean' in stats:
                print(f"   {metric_name}: {stats['mean']:.3f} (±{stats['std']:.3f})")
        print(f"📞 预估API调用次数: {api_call_count}")

        return final_result

    def _calculate_ragas_stats(self, results: List[Dict]) -> Dict:
        """计算RAGAS指标的统计信息"""
        stats = {}

        metrics = ['context_precision', 'context_recall', 'context_entity_recall']

        for metric_name in metrics:
            scores = []
            success_count = 0

            for result in results:
                metric_result = result['ragas_metrics'].get(metric_name, {})
                if 'score' in metric_result and metric_result.get('success', False):
                    scores.append(metric_result['score'])
                    success_count += 1

            if scores:
                stats[metric_name] = {
                    'mean': sum(scores) / len(scores),
                    'std': (sum((x - sum(scores)/len(scores))**2 for x in scores) / len(scores))**0.5,
                    'min': min(scores),
                    'max': max(scores),
                    'success_rate': success_count / len(results)
                }
            else:
                stats[metric_name] = {'error': 'No valid scores'}

        return stats
