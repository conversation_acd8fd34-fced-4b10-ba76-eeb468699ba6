#!/usr/bin/env python3
"""
DeepEval检索评估运行脚本

这个脚本使用DeepEval框架评估分块策略的检索质量，
集成到现有的项目结构中，使用已配置的阿里云API。
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.evaluation_framework.deepeval_evaluation import DeepEvalRetrievalEvaluation
from chunking_evaluation.chunking import FixedTokenChunker, RecursiveTokenChunker
from chunking_evaluation.utils import get_openai_embedding_function

def check_deepeval_installation():
    """检查DeepEval是否已安装"""
    try:
        import deepeval
        print("✅ DeepEval已安装")
        return True
    except ImportError:
        print("❌ DeepEval未安装")
        print("请运行: pip install deepeval")
        return False

def run_single_chunker_evaluation():
    """运行单个分块器的DeepEval评估"""
    
    print("🎯 DeepEval检索评估 - 单个分块器测试")
    print("="*80)
    
    # 配置路径
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/deepeval_test_db"
    
    # 检查数据集
    if not Path(questions_csv_path).exists():
        print(f"❌ 数据集文件不存在: {questions_csv_path}")
        return None
    
    # 创建评估器
    try:
        evaluator = DeepEvalRetrievalEvaluation(
            questions_csv_path=questions_csv_path,
            chroma_db_path=db_path
        )
        print("✅ DeepEval评估器初始化成功")
    except Exception as e:
        print(f"❌ 评估器初始化失败: {e}")
        return None
    
    # 创建分块器（使用表现最好的配置）
    chunker = FixedTokenChunker(chunk_size=200, chunk_overlap=20)
    print(f"📦 分块器: {chunker.__class__.__name__}(chunk_size=200, chunk_overlap=20)")
    
    # 获取嵌入函数
    try:
        embedding_function = get_openai_embedding_function()
        print("✅ 嵌入函数获取成功")
    except Exception as e:
        print(f"❌ 嵌入函数获取失败: {e}")
        return None
    
    # 运行评估
    try:
        print(f"\n🚀 开始DeepEval评估...")
        result = evaluator.run_deepeval_evaluation(
            chunker=chunker,
            embedding_function=embedding_function,
            retrieve=5,
            db_to_save_chunks=db_path,
            max_questions=1  # 限制为前1个问题进行测试
        )
        
        return result
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_multiple_chunkers_evaluation():
    """运行多个分块器的DeepEval评估"""
    
    print("🎯 DeepEval检索评估 - 多分块器对比")
    print("="*80)
    
    # 配置路径
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    
    # 定义要测试的分块器配置
    chunker_configs = [
        {"class": FixedTokenChunker, "params": {"chunk_size": 200, "chunk_overlap": 20}},
        {"class": FixedTokenChunker, "params": {"chunk_size": 500, "chunk_overlap": 50}},
        {"class": RecursiveTokenChunker, "params": {"chunk_size": 200, "chunk_overlap": 20}},
    ]
    
    # 获取嵌入函数
    embedding_function = get_openai_embedding_function()
    
    all_results = {}
    
    for i, config in enumerate(chunker_configs):
        chunker_class = config["class"]
        params = config["params"]
        
        print(f"\n📦 评估分块器 {i+1}/{len(chunker_configs)}: {chunker_class.__name__}")
        print(f"   参数: {params}")
        
        # 创建分块器
        chunker = chunker_class(**params)
        
        # 创建评估器
        db_path = f"chunking_evaluation/evaluation_framework/general_evaluation_data/deepeval_db_{i}"
        evaluator = DeepEvalRetrievalEvaluation(
            questions_csv_path=questions_csv_path,
            chroma_db_path=db_path
        )
        
        # 运行评估
        try:
            result = evaluator.run_deepeval_evaluation(
                chunker=chunker,
                embedding_function=embedding_function,
                retrieve=5,
                db_to_save_chunks=db_path,
                max_questions=10  # 评估前10个问题
            )
            
            config_name = result['chunker_config']
            all_results[config_name] = result
            
            print(f"✅ {config_name} 评估完成")
            
        except Exception as e:
            print(f"❌ {chunker_class.__name__} 评估失败: {e}")
    
    return all_results

def save_results(results, filename="deepeval_results.json"):
    """保存评估结果"""
    output_dir = Path("chunking_evaluation/evaluation_framework/outputs")
    output_dir.mkdir(exist_ok=True)
    
    output_file = output_dir / filename
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 结果已保存到: {output_file}")
    return output_file

def compare_with_original_results(deepeval_results):
    """与原始评估结果对比"""
    
    print(f"\n📊 DeepEval vs 原始评估对比")
    print("="*80)
    
    # 读取原始结果
    original_file = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if not Path(original_file).exists():
        print(f"❌ 原始结果文件不存在: {original_file}")
        return
    
    with open(original_file, 'r', encoding='utf-8') as f:
        original_results = json.load(f)
    
    print(f"📋 对比分析:")
    
    for config_name, deepeval_result in deepeval_results.items():
        print(f"\n🔍 配置: {config_name}")
        
        # DeepEval结果
        deepeval_metrics = deepeval_result.get('deepeval_metrics', {})
        
        print(f"   DeepEval指标:")
        for metric_name, stats in deepeval_metrics.items():
            if 'mean' in stats:
                print(f"     {metric_name}: {stats['mean']:.3f} (成功率: {stats['success_rate']:.1%})")
        
        # 原始结果
        if config_name in original_results:
            original_data = original_results[config_name]
            print(f"   原始指标:")
            print(f"     precision_mean: {original_data.get('precision_mean', 0):.3f}")
            print(f"     recall_mean: {original_data.get('recall_mean', 0):.3f}")
            print(f"     iou_mean: {original_data.get('iou_mean', 0):.3f}")
        else:
            print(f"   ⚠️  未找到对应的原始结果")

def main():
    """主函数"""
    
    print("🎯 DeepEval检索评估系统")
    print("="*100)
    
    # 检查DeepEval安装
    if not check_deepeval_installation():
        return
    
    print(f"\n📋 评估说明:")
    print(f"   - 使用项目中已配置的阿里云通义千问API")
    print(f"   - 只评估检索指标，不涉及生成部分")
    print(f"   - 评估指标: Contextual Precision, Contextual Recall, Contextual Relevancy")
    print(f"   - 与现有评估结果进行对比分析")
    
    # 选择评估模式
    print(f"\n🔧 选择评估模式:")
    print(f"   1. 单个分块器测试 (快速验证)")
    print(f"   2. 多分块器对比 (完整评估)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        # 单个分块器测试
        result = run_single_chunker_evaluation()
        
        if result:
            # 保存结果
            save_results({result['chunker_config']: result}, "deepeval_single_test.json")
            
            print(f"\n🎉 单个分块器测试完成!")
            print(f"📊 建议: 如果结果正常，可以运行完整的多分块器评估")
    
    elif choice == "2":
        # 多分块器评估
        results = run_multiple_chunkers_evaluation()
        
        if results:
            # 保存结果
            save_results(results, "deepeval_multiple_chunkers.json")
            
            # 与原始结果对比
            compare_with_original_results(results)
            
            print(f"\n🎉 多分块器评估完成!")
            print(f"📊 可以查看保存的结果文件进行详细分析")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
