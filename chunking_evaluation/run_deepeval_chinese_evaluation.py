#!/usr/bin/env python3
"""
中文数据集DeepEval分块评估脚本

这个脚本使用DeepEval框架评估不同分块策略在中文数据集上的表现。
参考run_chinese_evaluation.py的配置，但使用DeepEval的检索指标进行评估。
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.evaluation_framework.deepeval_evaluation import DeepEvalRetrievalEvaluation
from chunking_evaluation.chunking import FixedTokenChunker, RecursiveTokenChunker
from chunking_evaluation.utils import get_openai_embedding_function

def get_chunker_configs() -> Dict[str, List[Dict[str, Any]]]:
    """
    获取分块器配置（简化版，只测试两个配置）

    返回:
        Dict[str, List[Dict[str, Any]]]: 分块器名称到参数配置列表的映射
    """
    configs = {}

    # 只测试每种分块器的一个最佳配置
    configs['FixedTokenChunker'] = [
        {'chunk_size': 200, 'chunk_overlap': 20},  # 最佳配置
    ]

    configs['RecursiveTokenChunker'] = [
        {'chunk_size': 200, 'chunk_overlap': 20},  # 最佳配置
    ]

    return configs

def create_chunkers(configs: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Dict]:
    """
    根据配置创建分块器实例
    
    参数:
        configs: 分块器配置
        
    返回:
        Dict[str, Dict]: 分块器实例字典
    """
    chunkers = {}
    
    for chunker_type, param_configs in configs.items():
        for i, params in enumerate(param_configs):
            config_name = f"{chunker_type}_config_{i+1}"
            
            try:
                if chunker_type == 'FixedTokenChunker':
                    chunkers[config_name] = {
                        'chunker': FixedTokenChunker(**params),
                        'params': params,
                        'type': chunker_type
                    }
                
                elif chunker_type == 'RecursiveTokenChunker':
                    chunkers[config_name] = {
                        'chunker': RecursiveTokenChunker(**params),
                        'params': params,
                        'type': chunker_type
                    }
                
                print(f"✅ 创建分块器: {config_name} - {params}")
                
            except Exception as e:
                print(f"❌ 创建分块器失败 {config_name}: {e}")
    
    return chunkers

def run_deepeval_evaluation():
    """运行DeepEval评估"""
    
    print("🎯 中文数据集DeepEval分块评估")
    print("="*100)
    
    # 配置路径（与原始脚本相同）
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    base_db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data"
    
    # 检查数据集
    if not Path(questions_csv_path).exists():
        print(f"❌ 数据集文件不存在: {questions_csv_path}")
        return None
    
    # 获取嵌入函数
    try:
        embedding_function = get_openai_embedding_function()
        print("✅ 嵌入函数获取成功")
    except Exception as e:
        print(f"❌ 嵌入函数获取失败: {e}")
        return None
    
    # 获取分块器配置
    configs = get_chunker_configs()
    print(f"📦 分块器配置:")
    for chunker_type, param_configs in configs.items():
        print(f"   {chunker_type}: {len(param_configs)} 个配置")
    
    # 创建分块器
    chunkers = create_chunkers(configs)
    print(f"\n✅ 成功创建 {len(chunkers)} 个分块器")
    
    # 运行评估
    all_results = {}
    total_chunkers = len(chunkers)
    
    for i, (config_name, chunker_info) in enumerate(chunkers.items()):
        print(f"\n🔍 评估进度: {i+1}/{total_chunkers}")
        print(f"📦 当前配置: {config_name}")
        print(f"   类型: {chunker_info['type']}")
        print(f"   参数: {chunker_info['params']}")
        
        # 创建数据库路径
        db_path = f"{base_db_path}/deepeval_db_{config_name}"
        
        # 创建评估器
        try:
            evaluator = DeepEvalRetrievalEvaluation(
                questions_csv_path=questions_csv_path,
                chroma_db_path=db_path
            )
        except Exception as e:
            print(f"❌ 评估器创建失败: {e}")
            continue
        
        # 运行评估
        try:
            result = evaluator.run_deepeval_evaluation(
                chunker=chunker_info['chunker'],
                embedding_function=embedding_function,
                retrieve=5,  # 与原始脚本相同
                db_to_save_chunks=db_path,
                max_questions=3  # 只测试3个问题进行调试
            )
            
            # 生成与原始脚本兼容的配置名称
            params = chunker_info['params']
            compatible_config_name = f"{chunker_info['type']}_chunk_overlap{params['chunk_overlap']}_chunk_size{params['chunk_size']}"
            
            all_results[compatible_config_name] = result
            
            # 显示结果摘要
            deepeval_metrics = result.get('deepeval_metrics', {})
            print(f"   📊 DeepEval结果:")
            for metric_name, stats in deepeval_metrics.items():
                if 'mean' in stats:
                    print(f"     {metric_name}: {stats['mean']:.3f} (成功率: {stats['success_rate']:.1%})")
            
        except Exception as e:
            print(f"❌ 评估失败 {config_name}: {e}")
            import traceback
            traceback.print_exc()
    
    return all_results

def save_results(results: Dict, filename: str = "chinese_deepeval_results.json"):
    """保存评估结果（与原始脚本相同的目录）"""
    
    output_dir = Path("chunking_evaluation/evaluation_framework/outputs")
    output_dir.mkdir(exist_ok=True)
    
    output_file = output_dir / filename
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 DeepEval结果已保存到: {output_file}")
    return output_file

def generate_comparison_report(deepeval_results: Dict):
    """生成对比报告"""
    
    print(f"\n📊 DeepEval评估结果分析")
    print("="*100)
    
    if not deepeval_results:
        print("❌ 没有结果可分析")
        return
    
    # 按分块器类型分组分析
    chunker_types = {}
    for config_name, result in deepeval_results.items():
        if 'FixedTokenChunker' in config_name:
            chunker_type = 'FixedTokenChunker'
        elif 'RecursiveTokenChunker' in config_name:
            chunker_type = 'RecursiveTokenChunker'
        else:
            chunker_type = 'Unknown'
        
        if chunker_type not in chunker_types:
            chunker_types[chunker_type] = []
        
        chunker_types[chunker_type].append((config_name, result))
    
    # 分析每种分块器类型
    for chunker_type, configs in chunker_types.items():
        print(f"\n🔍 {chunker_type} 分析:")
        print(f"   配置数量: {len(configs)}")
        
        # 计算平均指标
        all_precision = []
        all_recall = []
        all_relevancy = []
        
        for config_name, result in configs:
            metrics = result.get('deepeval_metrics', {})
            
            if 'contextual_precision' in metrics and 'mean' in metrics['contextual_precision']:
                all_precision.append(metrics['contextual_precision']['mean'])
            
            if 'contextual_recall' in metrics and 'mean' in metrics['contextual_recall']:
                all_recall.append(metrics['contextual_recall']['mean'])
            
            if 'contextual_relevancy' in metrics and 'mean' in metrics['contextual_relevancy']:
                all_relevancy.append(metrics['contextual_relevancy']['mean'])
        
        if all_precision:
            print(f"   平均Contextual Precision: {sum(all_precision)/len(all_precision):.3f}")
        if all_recall:
            print(f"   平均Contextual Recall: {sum(all_recall)/len(all_recall):.3f}")
        if all_relevancy:
            print(f"   平均Contextual Relevancy: {sum(all_relevancy)/len(all_relevancy):.3f}")
        
        # 显示最佳配置
        if configs:
            best_config = max(configs, key=lambda x: x[1].get('deepeval_metrics', {}).get('contextual_precision', {}).get('mean', 0))
            print(f"   最佳配置: {best_config[0]}")

def compare_with_original_results(deepeval_results: Dict):
    """与原始评估结果对比"""
    
    print(f"\n🔄 DeepEval vs 原始评估对比")
    print("="*100)
    
    # 读取原始结果
    original_file = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if not Path(original_file).exists():
        print(f"❌ 原始结果文件不存在: {original_file}")
        return
    
    with open(original_file, 'r', encoding='utf-8') as f:
        original_results = json.load(f)
    
    print(f"📋 对比分析 (前5个配置):")
    
    count = 0
    for config_name, deepeval_result in deepeval_results.items():
        if count >= 5:
            break
        
        print(f"\n🔍 配置: {config_name}")
        
        # DeepEval结果
        deepeval_metrics = deepeval_result.get('deepeval_metrics', {})
        print(f"   DeepEval指标:")
        for metric_name, stats in deepeval_metrics.items():
            if 'mean' in stats:
                print(f"     {metric_name}: {stats['mean']:.3f}")
        
        # 原始结果
        if config_name in original_results:
            original_data = original_results[config_name]
            print(f"   原始指标:")
            print(f"     precision_mean: {original_data.get('precision_mean', 0):.3f}")
            print(f"     recall_mean: {original_data.get('recall_mean', 0):.3f}")
            print(f"     iou_mean: {original_data.get('iou_mean', 0):.3f}")
        else:
            print(f"   ⚠️  未找到对应的原始结果")
        
        count += 1

def main():
    """主函数"""
    
    print("🎯 中文数据集DeepEval评估系统")
    print("="*100)
    
    print(f"\n📋 评估说明:")
    print(f"   - 使用与run_chinese_evaluation.py相同的分块器配置")
    print(f"   - 评估FixedTokenChunker和RecursiveTokenChunker")
    print(f"   - 每种分块器6个参数配置")
    print(f"   - 使用DeepEval的3个检索指标")
    print(f"   - 结果保存到相同的outputs目录")
    
    # 运行评估
    results = run_deepeval_evaluation()
    
    if results:
        # 保存结果
        save_results(results)
        
        # 生成分析报告
        generate_comparison_report(results)
        
        # 与原始结果对比
        compare_with_original_results(results)
        
        print(f"\n🎉 DeepEval评估完成!")
        print(f"📊 总结:")
        print(f"   - 成功评估 {len(results)} 个配置")
        print(f"   - 结果已保存到outputs目录")
        print(f"   - 可以与原始评估结果进行对比分析")
    else:
        print(f"\n❌ 评估失败")

if __name__ == "__main__":
    main()
