#!/usr/bin/env python3
"""
使用DeepEval进行纯检索评估（不需要生成答案）

这个脚本展示如何使用您现有的数据集直接进行DeepEval评估，
无需修改数据集结构，专注于检索质量评估。
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def install_deepeval():
    """安装DeepEval"""
    import subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-U", "deepeval"])
        print("✅ DeepEval安装成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ DeepEval安装失败: {e}")
        return False

def setup_deepeval():
    """设置DeepEval环境"""
    try:
        from deepeval.metrics import (
            ContextualPrecisionMetric,
            ContextualRelevancyMetric
        )
        from deepeval.test_case import LLMTestCase
        print("✅ DeepEval导入成功!")
        return True
    except ImportError as e:
        print(f"❌ DeepEval导入失败: {e}")
        return False

def convert_to_deepeval_format(results_file: str) -> List[Dict]:
    """将您的数据集转换为DeepEval格式（纯检索评估）"""
    
    print(f"📋 转换数据集格式...")
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 选择最佳配置
    config_name = "FixedTokenChunker_chunk_overlap20_chunk_size200"
    if config_name not in results:
        print(f"❌ 未找到配置 {config_name}")
        return []
    
    questions = results[config_name].get('questions', [])
    
    test_cases = []
    for i, question_data in enumerate(questions[:10]):  # 测试前10个问题
        question_text = question_data.get('question_text', '')
        retrieval_results = question_data.get('retrieval_results', [])
        
        # 构建检索上下文
        retrieval_context = [
            result.get('document_content', '') 
            for result in retrieval_results
        ]
        
        test_case = {
            'input': question_text,
            'retrieval_context': retrieval_context,
            'question_index': i,
            'original_data': question_data  # 保留原始数据用于分析
        }
        
        test_cases.append(test_case)
    
    print(f"✅ 转换了 {len(test_cases)} 个测试用例")
    return test_cases

def run_retrieval_evaluation(test_cases: List[Dict]):
    """运行纯检索评估"""
    
    print("🔍 运行DeepEval纯检索评估...")
    
    try:
        from deepeval.metrics import (
            ContextualPrecisionMetric,
            ContextualRelevancyMetric
        )
        from deepeval.test_case import LLMTestCase
        
        # 创建评估指标（只使用不需要actual_output的指标）
        contextual_precision = ContextualPrecisionMetric(
            threshold=0.7,
            model="gpt-3.5-turbo",  # 使用更便宜的模型
            include_reason=True
        )
        
        contextual_relevancy = ContextualRelevancyMetric(
            threshold=0.7,
            model="gpt-3.5-turbo",
            include_reason=True
        )
        
        print(f"📊 评估指标:")
        print(f"   - Contextual Precision: 检索结果的排序质量")
        print(f"   - Contextual Relevancy: 检索内容与问题的相关性")
        print(f"   ⚠️  注意: 跳过Contextual Recall（需要expected_output）")
        
        # 检查API密钥
        if not os.getenv('OPENAI_API_KEY'):
            print(f"\n❌ 未找到OpenAI API密钥")
            print(f"请设置: export OPENAI_API_KEY='your-key'")
            return []
        
        results = []
        
        for i, case in enumerate(test_cases):
            print(f"\n🔍 评估问题 {i+1}/{len(test_cases)}")
            print(f"   问题: {case['input'][:50]}...")
            
            # 创建DeepEval测试用例（不需要actual_output和expected_output）
            test_case = LLMTestCase(
                input=case['input'],
                actual_output="",  # 空字符串，因为我们只评估检索
                retrieval_context=case['retrieval_context']
            )
            
            case_result = {
                'question_index': case['question_index'],
                'question': case['input'][:100] + "..." if len(case['input']) > 100 else case['input']
            }
            
            # 评估Contextual Precision
            try:
                contextual_precision.measure(test_case)
                case_result['contextual_precision'] = {
                    'score': contextual_precision.score,
                    'success': contextual_precision.success,
                    'reason': contextual_precision.reason
                }
                print(f"   Contextual Precision: {contextual_precision.score:.3f}")
            except Exception as e:
                print(f"   ❌ Contextual Precision 失败: {e}")
                case_result['contextual_precision'] = {'error': str(e)}
            
            # 评估Contextual Relevancy
            try:
                contextual_relevancy.measure(test_case)
                case_result['contextual_relevancy'] = {
                    'score': contextual_relevancy.score,
                    'success': contextual_relevancy.success,
                    'reason': contextual_relevancy.reason
                }
                print(f"   Contextual Relevancy: {contextual_relevancy.score:.3f}")
            except Exception as e:
                print(f"   ❌ Contextual Relevancy 失败: {e}")
                case_result['contextual_relevancy'] = {'error': str(e)}
            
            results.append(case_result)
        
        return results
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        return []

def analyze_results(results: List[Dict]):
    """分析评估结果"""
    
    if not results:
        print("❌ 没有结果可分析")
        return
    
    print(f"\n📊 DeepEval评估结果分析")
    print("="*80)
    
    # 提取分数
    precision_scores = []
    relevancy_scores = []
    
    for result in results:
        if 'contextual_precision' in result and 'score' in result['contextual_precision']:
            precision_scores.append(result['contextual_precision']['score'])
        
        if 'contextual_relevancy' in result and 'score' in result['contextual_relevancy']:
            relevancy_scores.append(result['contextual_relevancy']['score'])
    
    # 计算统计信息
    if precision_scores:
        print(f"\n🎯 Contextual Precision:")
        print(f"   平均分: {sum(precision_scores)/len(precision_scores):.3f}")
        print(f"   范围: {min(precision_scores):.3f} - {max(precision_scores):.3f}")
        print(f"   通过率: {sum(1 for s in precision_scores if s >= 0.7)/len(precision_scores)*100:.1f}%")
    
    if relevancy_scores:
        print(f"\n🎯 Contextual Relevancy:")
        print(f"   平均分: {sum(relevancy_scores)/len(relevancy_scores):.3f}")
        print(f"   范围: {min(relevancy_scores):.3f} - {max(relevancy_scores):.3f}")
        print(f"   通过率: {sum(1 for s in relevancy_scores if s >= 0.7)/len(relevancy_scores)*100:.1f}%")
    
    # 显示详细结果
    print(f"\n📋 详细结果:")
    for i, result in enumerate(results[:5]):  # 只显示前5个
        print(f"\n   问题 {i+1}: {result['question']}")
        
        if 'contextual_precision' in result and 'score' in result['contextual_precision']:
            score = result['contextual_precision']['score']
            print(f"     Precision: {score:.3f} {'✅' if score >= 0.7 else '❌'}")
        
        if 'contextual_relevancy' in result and 'score' in result['contextual_relevancy']:
            score = result['contextual_relevancy']['score']
            print(f"     Relevancy: {score:.3f} {'✅' if score >= 0.7 else '❌'}")

def compare_with_current_metrics():
    """与当前指标对比"""
    
    print(f"\n🔄 与您当前指标的对比")
    print("="*80)
    
    comparison = {
        "您当前的指标": {
            "精确率": "2-10% (位置匹配)",
            "召回率": "93-100% (位置匹配)",
            "IoU": "与精确率相同",
            "优势": "直接、可控、详细",
            "局限": "过于严格、不考虑语义"
        },
        "DeepEval检索指标": {
            "Contextual Precision": "0-1 (排序质量)",
            "Contextual Relevancy": "0-1 (语义相关性)",
            "Contextual Recall": "需要expected_output",
            "优势": "语义理解、标准化、业界认可",
            "局限": "需要API、成本较高"
        }
    }
    
    for method, metrics in comparison.items():
        print(f"\n📊 {method}:")
        for key, value in metrics.items():
            print(f"   {key}: {value}")

def main():
    """主函数"""
    
    print("🎯 使用DeepEval进行纯检索评估")
    print("="*100)
    
    # 检查DeepEval
    if not setup_deepeval():
        print("📦 安装DeepEval...")
        if install_deepeval():
            print("✅ 请重新运行脚本")
        return
    
    # 转换数据集
    results_file = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if not Path(results_file).exists():
        print(f"❌ 未找到结果文件: {results_file}")
        return
    
    test_cases = convert_to_deepeval_format(results_file)
    
    if not test_cases:
        print("❌ 数据转换失败")
        return
    
    print(f"\n✅ 数据集转换成功!")
    print(f"📋 关键优势:")
    print(f"   - 无需修改现有数据集")
    print(f"   - 无需LLM生成答案")
    print(f"   - 专注检索质量评估")
    print(f"   - 使用业界标准指标")
    
    # 运行评估
    results = run_retrieval_evaluation(test_cases)
    
    if results:
        # 分析结果
        analyze_results(results)
        
        # 保存结果
        output_file = "deepeval_retrieval_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {output_file}")
    
    # 对比分析
    compare_with_current_metrics()
    
    print(f"\n🎉 评估完成!")
    print(f"\n📋 总结:")
    print(f"   ✅ 您的数据集完全适用于DeepEval")
    print(f"   ✅ 无需修改数据集结构")
    print(f"   ✅ 可以获得标准化的检索评估指标")
    print(f"   ✅ 建议同时保留您当前的详细分析")

if __name__ == "__main__":
    main()
