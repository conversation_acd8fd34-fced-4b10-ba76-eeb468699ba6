#!/usr/bin/env python3
"""
扩展数据集以支持完整的DeepEval评估

这个脚本展示如何从您的ground truth位置信息中提取实际文本，
从而支持DeepEval的所有检索指标，包括Contextual Recall。
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def extract_ground_truth_text(question_data: Dict, document_content: str) -> str:
    """从ground truth位置信息中提取实际文本"""
    
    ground_truth = question_data.get('ground_truth', [])
    if not ground_truth:
        return "未找到标准答案"
    
    # 提取所有ground truth片段的文本
    extracted_texts = []
    
    for gt in ground_truth:
        start_idx = gt.get('start_index', 0)
        end_idx = gt.get('end_index', 0)
        
        if start_idx < len(document_content) and end_idx <= len(document_content):
            text_fragment = document_content[start_idx:end_idx]
            extracted_texts.append(text_fragment.strip())
    
    # 合并所有片段
    if extracted_texts:
        return " ".join(extracted_texts)
    else:
        return "无法提取标准答案文本"

def load_original_document():
    """加载原始文档内容"""
    
    # 这里需要根据您的实际文档路径调整
    doc_paths = [
        "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_document.txt",
        "chunking_evaluation/evaluation_framework/general_evaluation_data/document.txt",
        # 添加其他可能的文档路径
    ]
    
    for doc_path in doc_paths:
        if Path(doc_path).exists():
            print(f"📄 找到文档: {doc_path}")
            with open(doc_path, 'r', encoding='utf-8') as f:
                return f.read()
    
    print("❌ 未找到原始文档文件")
    print("请确保以下文件之一存在:")
    for path in doc_paths:
        print(f"   - {path}")
    
    return None

def create_extended_dataset(results_file: str) -> List[Dict]:
    """创建扩展的数据集，包含expected_output"""
    
    print(f"📋 创建扩展数据集...")
    
    # 加载原始文档
    document_content = load_original_document()
    if not document_content:
        print("❌ 无法加载原始文档，使用简化方案")
        return []
    
    # 加载评估结果
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    config_name = "FixedTokenChunker_chunk_overlap20_chunk_size200"
    if config_name not in results:
        print(f"❌ 未找到配置 {config_name}")
        return []
    
    questions = results[config_name].get('questions', [])
    
    extended_test_cases = []
    
    for i, question_data in enumerate(questions[:10]):
        question_text = question_data.get('question_text', '')
        retrieval_results = question_data.get('retrieval_results', [])
        
        # 构建检索上下文
        retrieval_context = [
            result.get('document_content', '') 
            for result in retrieval_results
        ]
        
        # 提取expected_output
        expected_output = extract_ground_truth_text(question_data, document_content)
        
        test_case = {
            'input': question_text,
            'expected_output': expected_output,
            'actual_output': "",  # 仍然为空，因为我们只评估检索
            'retrieval_context': retrieval_context,
            'question_index': i,
            'ground_truth_info': question_data.get('ground_truth', [])
        }
        
        extended_test_cases.append(test_case)
        
        print(f"   问题 {i+1}: {question_text[:50]}...")
        print(f"     Expected: {expected_output[:100]}...")
    
    print(f"✅ 创建了 {len(extended_test_cases)} 个扩展测试用例")
    return extended_test_cases

def run_full_retrieval_evaluation(test_cases: List[Dict]):
    """运行完整的检索评估（包括Contextual Recall）"""
    
    print("🔍 运行完整的DeepEval检索评估...")
    
    try:
        from deepeval.metrics import (
            ContextualPrecisionMetric,
            ContextualRecallMetric,
            ContextualRelevancyMetric
        )
        from deepeval.test_case import LLMTestCase
        
        # 创建所有检索评估指标
        contextual_precision = ContextualPrecisionMetric(threshold=0.7)
        contextual_recall = ContextualRecallMetric(threshold=0.7)
        contextual_relevancy = ContextualRelevancyMetric(threshold=0.7)
        
        print(f"📊 评估指标:")
        print(f"   - Contextual Precision: 检索结果的排序质量")
        print(f"   - Contextual Recall: 检索内容是否足以生成期望答案")
        print(f"   - Contextual Relevancy: 检索内容与问题的相关性")
        
        # 检查API密钥
        if not os.getenv('OPENAI_API_KEY'):
            print(f"\n❌ 未找到OpenAI API密钥")
            return []
        
        results = []
        
        for i, case in enumerate(test_cases):
            print(f"\n🔍 评估问题 {i+1}/{len(test_cases)}")
            
            # 创建DeepEval测试用例
            test_case = LLMTestCase(
                input=case['input'],
                expected_output=case['expected_output'],
                actual_output=case['actual_output'],
                retrieval_context=case['retrieval_context']
            )
            
            case_result = {
                'question_index': case['question_index'],
                'question': case['input'][:100] + "..." if len(case['input']) > 100 else case['input'],
                'expected_output': case['expected_output'][:100] + "..." if len(case['expected_output']) > 100 else case['expected_output']
            }
            
            # 评估所有指标
            metrics = [
                ('contextual_precision', contextual_precision),
                ('contextual_recall', contextual_recall),
                ('contextual_relevancy', contextual_relevancy)
            ]
            
            for metric_name, metric in metrics:
                try:
                    metric.measure(test_case)
                    case_result[metric_name] = {
                        'score': metric.score,
                        'success': metric.success,
                        'reason': metric.reason
                    }
                    print(f"   {metric_name}: {metric.score:.3f}")
                except Exception as e:
                    print(f"   ❌ {metric_name} 失败: {e}")
                    case_result[metric_name] = {'error': str(e)}
            
            results.append(case_result)
        
        return results
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        return []

def analyze_full_results(results: List[Dict]):
    """分析完整评估结果"""
    
    if not results:
        return
    
    print(f"\n📊 完整DeepEval评估结果")
    print("="*80)
    
    metrics = ['contextual_precision', 'contextual_recall', 'contextual_relevancy']
    
    for metric_name in metrics:
        scores = []
        for result in results:
            if metric_name in result and 'score' in result[metric_name]:
                scores.append(result[metric_name]['score'])
        
        if scores:
            print(f"\n🎯 {metric_name.replace('_', ' ').title()}:")
            print(f"   平均分: {sum(scores)/len(scores):.3f}")
            print(f"   范围: {min(scores):.3f} - {max(scores):.3f}")
            print(f"   通过率: {sum(1 for s in scores if s >= 0.7)/len(scores)*100:.1f}%")

def provide_implementation_guide():
    """提供实施指南"""
    
    print(f"\n💡 实施指南")
    print("="*80)
    
    guide = {
        "数据集要求": [
            "✅ 您当前的数据集已经包含所需信息",
            "✅ ground_truth位置信息可以提取实际文本",
            "✅ 检索结果已经包含上下文信息",
            "⚠️  需要确保原始文档文件可访问"
        ],
        "API要求": [
            "🔑 需要OpenAI API密钥",
            "💰 评估成本约$0.01-0.05每个问题",
            "⚡ 可以使用gpt-3.5-turbo降低成本",
            "🔄 支持批量评估"
        ],
        "输出格式": [
            "📊 标准化的0-1分数",
            "📝 详细的评估原因",
            "✅ 通过/失败状态",
            "📈 可与其他系统对比"
        ]
    }
    
    for category, items in guide.items():
        print(f"\n📋 {category}:")
        for item in items:
            print(f"   {item}")

def main():
    """主函数"""
    
    print("🎯 扩展数据集支持完整DeepEval评估")
    print("="*100)
    
    # 检查结果文件
    results_file = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if not Path(results_file).exists():
        print(f"❌ 未找到结果文件: {results_file}")
        return
    
    # 创建扩展数据集
    extended_test_cases = create_extended_dataset(results_file)
    
    if not extended_test_cases:
        print("❌ 数据集扩展失败")
        return
    
    print(f"\n✅ 数据集扩展成功!")
    print(f"📋 扩展内容:")
    print(f"   - 从ground_truth位置提取expected_output")
    print(f"   - 保留所有原始检索信息")
    print(f"   - 支持所有DeepEval检索指标")
    
    # 保存扩展数据集
    output_file = "extended_deepeval_dataset.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(extended_test_cases, f, ensure_ascii=False, indent=2)
    print(f"💾 扩展数据集已保存到: {output_file}")
    
    # 提供实施指南
    provide_implementation_guide()
    
    print(f"\n🎉 数据集扩展完成!")
    print(f"\n📋 下一步:")
    print(f"   1. 设置OpenAI API密钥")
    print(f"   2. 运行完整评估: python run_full_deepeval.py")
    print(f"   3. 对比分析结果")

if __name__ == "__main__":
    main()
