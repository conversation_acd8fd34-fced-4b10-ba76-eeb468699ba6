#!/usr/bin/env python3
"""
评估指标深度分析和改进建议

这个脚本提供了对当前评估系统的深度分析和具体的改进建议。
"""

import json
import numpy as np
from pathlib import Path

def analyze_current_results():
    """分析当前评估结果的问题"""
    
    print("📊 当前评估结果深度分析")
    print("="*80)
    
    # 读取当前结果
    results_path = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if not Path(results_path).exists():
        print(f"❌ 结果文件不存在: {results_path}")
        return
    
    with open(results_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print(f"📋 分析配置数量: {len(results)}")
    
    # 分析每个配置的指标
    for config_name, config_data in results.items():
        if 'questions' not in config_data:
            continue
            
        questions = config_data['questions']
        
        # 提取指标
        precisions = [q['metrics']['precision_score'] for q in questions if 'metrics' in q]
        recalls = [q['metrics']['recall_score'] for q in questions if 'metrics' in q]
        ious = [q['metrics']['iou_score'] for q in questions if 'metrics' in q]
        
        if not precisions:
            continue
        
        # 计算统计信息
        precision_mean = np.mean(precisions)
        recall_mean = np.mean(recalls)
        iou_mean = np.mean(ious)
        
        # 检查IoU是否等于精确率
        iou_equals_precision = all(abs(p - i) < 0.0001 for p, i in zip(precisions, ious))
        
        print(f"\n🔍 {config_name}:")
        print(f"   精确率: {precision_mean:.4f} (范围: {min(precisions):.4f}-{max(precisions):.4f})")
        print(f"   召回率: {recall_mean:.4f} (范围: {min(recalls):.4f}-{max(recalls):.4f})")
        print(f"   IoU分数: {iou_mean:.4f} (范围: {min(ious):.4f}-{max(ious):.4f})")
        print(f"   IoU=精确率: {'是' if iou_equals_precision else '否'}")
        
        # 分析问题模式
        high_recall_low_precision = recall_mean > 0.9 and precision_mean < 0.15
        if high_recall_low_precision:
            print(f"   ⚠️  典型问题: 高召回率({recall_mean:.3f})但低精确率({precision_mean:.3f})")

def calculate_theoretical_metrics():
    """计算理论上的正确指标示例"""
    
    print(f"\n🧮 理论指标计算示例")
    print("="*80)
    
    # 示例场景
    scenarios = [
        {
            "name": "完全匹配",
            "retrieved_chunks": [(0, 100)],
            "ground_truth": [(20, 80)],
            "intersection": [(20, 80)]
        },
        {
            "name": "部分重叠",
            "retrieved_chunks": [(0, 50), (70, 120)],
            "ground_truth": [(40, 90)],
            "intersection": [(40, 50), (70, 90)]
        },
        {
            "name": "无重叠",
            "retrieved_chunks": [(0, 30)],
            "ground_truth": [(50, 80)],
            "intersection": []
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📝 场景: {scenario['name']}")
        
        # 计算长度
        retrieved_length = sum(end - start for start, end in scenario['retrieved_chunks'])
        ground_truth_length = sum(end - start for start, end in scenario['ground_truth'])
        intersection_length = sum(end - start for start, end in scenario['intersection'])
        
        # 计算指标
        precision = intersection_length / retrieved_length if retrieved_length > 0 else 0
        recall = intersection_length / ground_truth_length if ground_truth_length > 0 else 0
        
        # 正确的IoU计算
        union_length = retrieved_length + ground_truth_length - intersection_length
        iou_correct = intersection_length / union_length if union_length > 0 else 0
        
        # 错误的IoU计算（当前实现）
        unused_ground_truth = ground_truth_length - intersection_length
        iou_wrong = intersection_length / (retrieved_length + unused_ground_truth) if (retrieved_length + unused_ground_truth) > 0 else 0
        
        print(f"   检索分块长度: {retrieved_length}")
        print(f"   参考答案长度: {ground_truth_length}")
        print(f"   交集长度: {intersection_length}")
        print(f"   精确率: {precision:.4f}")
        print(f"   召回率: {recall:.4f}")
        print(f"   正确IoU: {iou_correct:.4f}")
        print(f"   错误IoU: {iou_wrong:.4f}")
        print(f"   IoU差异: {abs(iou_correct - iou_wrong):.4f}")

def generate_improvement_recommendations():
    """生成改进建议"""
    
    print(f"\n💡 改进建议")
    print("="*80)
    
    recommendations = [
        {
            "category": "1. 代码修复",
            "items": [
                "✅ 已修复IoU计算公式",
                "🔄 需要验证_full_precision_score方法",
                "🔄 添加更多的边界情况处理",
                "🔄 改进数值稳定性"
            ]
        },
        {
            "category": "2. 评估标准优化",
            "items": [
                "📏 引入部分匹配阈值（如0.5重叠度）",
                "📐 考虑语义相似度而非仅位置匹配",
                "📊 添加F1分数等综合指标",
                "🎯 针对不同分块大小调整评估标准"
            ]
        },
        {
            "category": "3. 分块策略优化",
            "items": [
                "📦 测试更小的分块大小（100-300 tokens）",
                "🔄 优化重叠度参数",
                "🧠 考虑语义分块而非固定长度分块",
                "⚖️ 平衡精确率和召回率"
            ]
        },
        {
            "category": "4. 数据集改进",
            "items": [
                "📝 重新审查参考答案的标注质量",
                "📏 确保参考答案长度的合理性",
                "🎯 增加更多样化的问题类型",
                "✅ 添加质量控制流程"
            ]
        }
    ]
    
    for rec in recommendations:
        print(f"\n{rec['category']}:")
        for item in rec['items']:
            print(f"   {item}")

def main():
    """主函数"""
    
    print("🎯 文本分块评估系统深度分析")
    print("="*100)
    
    # 分析当前结果
    analyze_current_results()
    
    # 计算理论指标
    calculate_theoretical_metrics()
    
    # 生成改进建议
    generate_improvement_recommendations()
    
    print(f"\n🎉 分析完成!")
    print(f"\n📋 总结:")
    print(f"   - 确认了IoU计算公式的错误")
    print(f"   - 精确率低主要由于分块粒度不匹配")
    print(f"   - 需要综合优化评估标准和分块策略")
    print(f"   - 建议重新运行完整评估以验证修复效果")

if __name__ == "__main__":
    main()
