#!/usr/bin/env python3
"""
将您的数据集转换为DeepEval格式

这个脚本展示了如何将您现有的数据集和检索结果转换为DeepEval所需的格式，
实现完美的数据对应关系。
"""

import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any

def load_questions_dataset(csv_path: str) -> pd.DataFrame:
    """加载问题数据集"""
    print(f"📋 加载问题数据集: {csv_path}")
    
    df = pd.read_csv(csv_path, encoding='utf-8')
    
    # 解析references列（JSON字符串转为Python对象）
    df['references'] = df['references'].apply(json.loads)
    
    print(f"✅ 加载了 {len(df)} 个问题")
    return df

def load_evaluation_results(json_path: str) -> Dict:
    """加载评估结果"""
    print(f"📋 加载评估结果: {json_path}")
    
    with open(json_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print(f"✅ 加载了 {len(results)} 个配置的评估结果")
    return results

def convert_single_question_to_deepeval(question_data: Dict, retrieval_results: List[Dict]) -> Dict:
    """将单个问题转换为DeepEval格式"""
    
    # 提取问题文本
    input_text = question_data['question']
    
    # 提取期望答案（从references中合并所有content）
    references = question_data['references']
    expected_output = " ".join([ref['content'] for ref in references])
    
    # 提取检索上下文
    retrieval_context = [
        result['document_content'] 
        for result in retrieval_results
    ]
    
    # 构建DeepEval测试用例数据
    deepeval_case = {
        'input': input_text,
        'expected_output': expected_output,
        'actual_output': "",  # 空字符串，因为我们只评估检索
        'retrieval_context': retrieval_context,
        
        # 保留原始数据用于调试
        'original_question_data': question_data,
        'original_retrieval_results': retrieval_results
    }
    
    return deepeval_case

def convert_dataset_to_deepeval(questions_df: pd.DataFrame, evaluation_results: Dict, 
                               config_name: str = "FixedTokenChunker_chunk_overlap20_chunk_size200") -> List[Dict]:
    """将整个数据集转换为DeepEval格式"""
    
    print(f"🔄 转换配置: {config_name}")
    
    if config_name not in evaluation_results:
        print(f"❌ 未找到配置 {config_name}")
        available_configs = list(evaluation_results.keys())
        print(f"可用配置: {available_configs}")
        return []
    
    config_results = evaluation_results[config_name]
    questions_with_results = config_results.get('questions', [])
    
    deepeval_cases = []
    
    for i, question_result in enumerate(questions_with_results):
        # 从评估结果中获取问题信息
        question_text = question_result.get('question_text', '')
        corpus_id = question_result.get('corpus_id', '')
        retrieval_results = question_result.get('retrieval_results', [])
        
        # 在原始数据集中找到对应的问题
        matching_questions = questions_df[
            (questions_df['question'] == question_text) & 
            (questions_df['corpus_id'] == corpus_id)
        ]
        
        if len(matching_questions) == 0:
            print(f"⚠️  警告: 未找到匹配的问题 {i}: {question_text[:50]}...")
            continue
        
        # 获取第一个匹配的问题数据
        question_data = matching_questions.iloc[0].to_dict()
        
        # 转换为DeepEval格式
        deepeval_case = convert_single_question_to_deepeval(question_data, retrieval_results)
        deepeval_case['question_index'] = i
        
        deepeval_cases.append(deepeval_case)
        
        print(f"✅ 转换问题 {i+1}: {question_text[:50]}...")
    
    print(f"🎉 成功转换 {len(deepeval_cases)} 个问题")
    return deepeval_cases

def analyze_data_mapping(deepeval_cases: List[Dict]):
    """分析数据映射情况"""
    
    print(f"\n📊 数据映射分析")
    print("="*80)
    
    if not deepeval_cases:
        print("❌ 没有数据可分析")
        return
    
    # 分析第一个案例
    first_case = deepeval_cases[0]
    
    print(f"📋 示例数据映射 (第1个问题):")
    print(f"   Input: {first_case['input'][:100]}...")
    print(f"   Expected Output: {first_case['expected_output'][:100]}...")
    print(f"   Retrieval Context数量: {len(first_case['retrieval_context'])}")
    
    # 统计信息
    input_lengths = [len(case['input']) for case in deepeval_cases]
    expected_lengths = [len(case['expected_output']) for case in deepeval_cases]
    context_counts = [len(case['retrieval_context']) for case in deepeval_cases]
    
    print(f"\n📈 统计信息:")
    print(f"   问题数量: {len(deepeval_cases)}")
    print(f"   问题长度: {min(input_lengths)}-{max(input_lengths)} 字符")
    print(f"   期望答案长度: {min(expected_lengths)}-{max(expected_lengths)} 字符")
    print(f"   检索上下文数量: {min(context_counts)}-{max(context_counts)} 个")
    
    # 检查数据完整性
    incomplete_cases = 0
    for case in deepeval_cases:
        if not case['input'] or not case['expected_output'] or not case['retrieval_context']:
            incomplete_cases += 1
    
    print(f"   数据完整性: {len(deepeval_cases) - incomplete_cases}/{len(deepeval_cases)} 完整")
    
    if incomplete_cases > 0:
        print(f"⚠️  警告: {incomplete_cases} 个案例数据不完整")

def demonstrate_deepeval_usage(deepeval_cases: List[Dict]):
    """演示如何使用转换后的数据"""
    
    print(f"\n💡 DeepEval使用示例")
    print("="*80)
    
    if not deepeval_cases:
        return
    
    print(f"```python")
    print(f"from deepeval.metrics import (")
    print(f"    ContextualPrecisionMetric,")
    print(f"    ContextualRecallMetric,")
    print(f"    ContextualRelevancyMetric")
    print(f")")
    print(f"from deepeval.test_case import LLMTestCase")
    print(f"")
    print(f"# 创建评估指标")
    print(f"contextual_precision = ContextualPrecisionMetric(threshold=0.7)")
    print(f"contextual_recall = ContextualRecallMetric(threshold=0.7)")
    print(f"contextual_relevancy = ContextualRelevancyMetric(threshold=0.7)")
    print(f"")
    print(f"# 使用转换后的数据")
    print(f"case_data = deepeval_cases[0]  # 第一个问题")
    print(f"")
    print(f"test_case = LLMTestCase(")
    print(f"    input=case_data['input'],")
    print(f"    expected_output=case_data['expected_output'],")
    print(f"    actual_output=case_data['actual_output'],")
    print(f"    retrieval_context=case_data['retrieval_context']")
    print(f")")
    print(f"")
    print(f"# 运行评估")
    print(f"contextual_precision.measure(test_case)")
    print(f"print(f'Contextual Precision: {{contextual_precision.score:.3f}}')")
    print(f"```")

def main():
    """主函数"""
    
    print("🎯 数据集格式转换：您的数据 → DeepEval格式")
    print("="*100)
    
    # 文件路径
    questions_csv = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    results_json = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    # 检查文件存在性
    if not Path(questions_csv).exists():
        print(f"❌ 问题数据集文件不存在: {questions_csv}")
        return
    
    if not Path(results_json).exists():
        print(f"❌ 评估结果文件不存在: {results_json}")
        return
    
    # 加载数据
    questions_df = load_questions_dataset(questions_csv)
    evaluation_results = load_evaluation_results(results_json)
    
    # 转换数据
    deepeval_cases = convert_dataset_to_deepeval(
        questions_df, 
        evaluation_results, 
        config_name="FixedTokenChunker_chunk_overlap20_chunk_size200"
    )
    
    if deepeval_cases:
        # 保存转换结果
        output_file = "deepeval_converted_dataset.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(deepeval_cases, f, ensure_ascii=False, indent=2)
        print(f"\n💾 转换结果已保存到: {output_file}")
        
        # 分析数据映射
        analyze_data_mapping(deepeval_cases)
        
        # 演示使用方法
        demonstrate_deepeval_usage(deepeval_cases)
        
        print(f"\n🎉 数据转换完成!")
        print(f"📋 总结:")
        print(f"   ✅ 您的数据集完美适配DeepEval")
        print(f"   ✅ 所有必需字段都有对应数据")
        print(f"   ✅ 可以直接用于检索评估")
        print(f"   ✅ 支持所有3个检索指标")
    else:
        print(f"❌ 数据转换失败")

if __name__ == "__main__":
    main()
