#!/usr/bin/env python3
"""
使用RAGAs框架进行RAG评估

RAGAs是专门为RAG系统设计的评估框架，提供了标准的评估指标。
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def install_ragas():
    """安装RAGAs框架"""
    import subprocess
    
    print("📦 安装RAGAs框架...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-U", "ragas"])
        print("✅ RAGAs安装成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ RAGAs安装失败: {e}")
        return False

def setup_ragas_evaluation():
    """设置RAGAs评估环境"""
    
    try:
        from ragas import evaluate
        from ragas.metrics import (
            faithfulness,
            answer_relevancy,
            context_precision,
            context_recall,
            context_relevancy
        )
        from datasets import Dataset
        print("✅ RAGAs导入成功!")
        return True
    except ImportError as e:
        print(f"❌ RAGAs导入失败: {e}")
        print("请先运行: pip install -U ragas datasets")
        return False

def prepare_ragas_dataset(results_file: str) -> Dict[str, List]:
    """准备RAGAs评估数据集"""
    
    print(f"📋 准备RAGAs评估数据集...")
    
    # 读取现有结果
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 选择最佳配置
    config_name = "FixedTokenChunker_chunk_overlap20_chunk_size200"
    if config_name not in results:
        print(f"❌ 未找到配置 {config_name}")
        return {}
    
    questions = results[config_name].get('questions', [])
    
    # 准备RAGAs格式的数据
    dataset = {
        'question': [],
        'answer': [],
        'contexts': [],
        'ground_truth': []
    }
    
    for question_data in questions[:10]:  # 限制为前10个问题
        question_text = question_data.get('question_text', '')
        ground_truth = question_data.get('ground_truth', [])
        retrieval_results = question_data.get('retrieval_results', [])
        
        # 构建上下文列表
        contexts = [
            result.get('document_content', '') 
            for result in retrieval_results
        ]
        
        # 构建ground truth答案
        if ground_truth:
            gt_text = f"答案位于文档的第{ground_truth[0].get('start_index', 0)}-{ground_truth[0].get('end_index', 0)}位置"
        else:
            gt_text = "未找到标准答案"
        
        dataset['question'].append(question_text)
        dataset['answer'].append("这里需要LLM生成的实际答案")  # 需要实际RAG系统生成
        dataset['contexts'].append(contexts)
        dataset['ground_truth'].append(gt_text)
    
    print(f"✅ 准备了 {len(dataset['question'])} 个评估样本")
    return dataset

def run_ragas_evaluation(dataset: Dict[str, List]):
    """运行RAGAs评估"""
    
    print("🔍 运行RAGAs标准评估...")
    
    try:
        from ragas import evaluate
        from ragas.metrics import (
            context_precision,
            context_recall,
            context_relevancy,
            faithfulness,
            answer_relevancy
        )
        from datasets import Dataset
        
        # 转换为Dataset格式
        eval_dataset = Dataset.from_dict(dataset)
        
        # 定义评估指标
        metrics = [
            context_precision,      # 上下文精确率
            context_recall,         # 上下文召回率
            context_relevancy,      # 上下文相关性
            faithfulness,           # 忠实度
            answer_relevancy        # 答案相关性
        ]
        
        print(f"📊 评估指标:")
        print(f"   - Context Precision: 检索上下文的精确率")
        print(f"   - Context Recall: 检索上下文的召回率")
        print(f"   - Context Relevancy: 上下文与问题的相关性")
        print(f"   - Faithfulness: 答案对上下文的忠实度")
        print(f"   - Answer Relevancy: 答案与问题的相关性")
        
        # 运行评估
        print(f"\n⚠️  注意: 完整评估需要OpenAI API密钥")
        print(f"请设置环境变量: export OPENAI_API_KEY='your-key'")
        
        # 检查API密钥
        if not os.getenv('OPENAI_API_KEY'):
            print(f"❌ 未找到OpenAI API密钥，跳过实际评估")
            return None
        
        # 运行评估
        result = evaluate(
            eval_dataset,
            metrics=metrics,
        )
        
        print(f"\n✅ RAGAs评估完成!")
        print(f"📊 评估结果:")
        
        for metric_name, score in result.items():
            print(f"   {metric_name}: {score:.4f}")
        
        return result
        
    except Exception as e:
        print(f"❌ RAGAs评估失败: {e}")
        return None

def analyze_chunking_performance():
    """分析分块性能的正确理解"""
    
    print(f"\n🎓 分块评估的正确理解")
    print("="*80)
    
    explanations = [
        {
            "concept": "为什么精确率低是正常的",
            "explanation": [
                "分块包含目标信息 + 大量上下文信息",
                "精确率 = 相关信息长度 / 总分块长度",
                "即使完全包含答案，精确率也必然很低",
                "这是RAG系统的设计特性，不是缺陷"
            ]
        },
        {
            "concept": "分块评估的真正目标",
            "explanation": [
                "确保相关信息被检索到（高召回率）",
                "为LLM提供足够的上下文信息",
                "平衡信息完整性和处理效率",
                "支持后续的精确信息提取"
            ]
        },
        {
            "concept": "正确的评估重点",
            "explanation": [
                "端到端的答案质量",
                "用户问题的满意度",
                "系统的响应速度",
                "整体的用户体验"
            ]
        }
    ]
    
    for item in explanations:
        print(f"\n📚 {item['concept']}:")
        for point in item['explanation']:
            print(f"   • {point}")

def provide_framework_comparison():
    """提供评估框架对比"""
    
    print(f"\n🔧 评估框架对比")
    print("="*80)
    
    frameworks = {
        "DeepEval": {
            "优势": [
                "50+ 评估指标",
                "支持多种LLM应用",
                "Pytest集成",
                "活跃的社区支持"
            ],
            "适用场景": "全面的LLM应用评估",
            "安装": "pip install deepeval"
        },
        "RAGAs": {
            "优势": [
                "专门为RAG设计",
                "简单易用",
                "标准化指标",
                "学术认可度高"
            ],
            "适用场景": "专门的RAG系统评估",
            "安装": "pip install ragas"
        },
        "您当前的方法": {
            "优势": [
                "直接的位置匹配",
                "可控的评估逻辑",
                "自定义指标",
                "详细的调试信息"
            ],
            "适用场景": "分块策略的细粒度分析",
            "建议": "结合标准框架使用"
        }
    }
    
    for framework, details in frameworks.items():
        print(f"\n🔍 {framework}:")
        print(f"   优势: {', '.join(details['优势'])}")
        print(f"   适用: {details['适用场景']}")
        if '安装' in details:
            print(f"   安装: {details['安装']}")
        if '建议' in details:
            print(f"   建议: {details['建议']}")

def main():
    """主函数"""
    
    print("🎯 使用RAGAs框架评估RAG系统")
    print("="*100)
    
    # 检查RAGAs安装
    if not setup_ragas_evaluation():
        print("\n📦 尝试安装RAGAs...")
        if install_ragas():
            print("✅ 请重新运行脚本以使用RAGAs")
        return
    
    # 分析现有结果
    results_file = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if Path(results_file).exists():
        print(f"\n📋 分析现有评估结果...")
        
        # 准备数据集
        dataset = prepare_ragas_dataset(results_file)
        
        if dataset:
            print(f"\n⚠️  注意: 完整的RAGAs评估需要:")
            print(f"   1. OpenAI API密钥 (用于LLM评估)")
            print(f"   2. 实际的RAG系统生成答案")
            print(f"   3. 完整的问答对数据")
            
            # 可以运行评估（如果有API密钥）
            # run_ragas_evaluation(dataset)
    
    # 分析分块性能理解
    analyze_chunking_performance()
    
    # 框架对比
    provide_framework_comparison()
    
    print(f"\n🎉 分析完成!")
    print(f"\n📋 关键建议:")
    print(f"   ✅ 您的分块评估结果是正常的")
    print(f"   ✅ 低精确率是分块的固有特性")
    print(f"   ✅ 重点应该放在端到端RAG性能")
    print(f"   ✅ 建议集成RAGAs或DeepEval进行全面评估")

if __name__ == "__main__":
    main()
