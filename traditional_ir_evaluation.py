#!/usr/bin/env python3
"""
使用传统信息检索评估框架

这个脚本展示如何使用pytrec_eval等标准IR工具评估您的检索系统，
无需API调用，完全基于您现有的数据集。
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def install_pytrec_eval():
    """安装pytrec_eval"""
    import subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pytrec_eval"])
        print("✅ pytrec_eval安装成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ pytrec_eval安装失败: {e}")
        return False

def setup_pytrec_eval():
    """设置pytrec_eval环境"""
    try:
        import pytrec_eval
        print("✅ pytrec_eval导入成功!")
        return True
    except ImportError as e:
        print(f"❌ pytrec_eval导入失败: {e}")
        return False

def convert_to_trec_format(results_file: str) -> Tuple[Dict, Dict]:
    """将您的数据集转换为TREC格式"""
    
    print(f"📋 转换为TREC格式...")
    
    with open(results_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    config_name = "FixedTokenChunker_chunk_overlap20_chunk_size200"
    if config_name not in results:
        print(f"❌ 未找到配置 {config_name}")
        return {}, {}
    
    questions = results[config_name].get('questions', [])
    
    # TREC格式的qrel（相关性判断）
    qrel = {}
    # TREC格式的run（检索结果）
    run = {}
    
    for i, question_data in enumerate(questions[:20]):  # 处理前20个问题
        query_id = str(i)
        ground_truth = question_data.get('ground_truth', [])
        retrieval_results = question_data.get('retrieval_results', [])
        
        # 构建qrel：标记哪些文档是相关的
        qrel[query_id] = {}
        
        # 为每个检索结果分配相关性分数
        for j, result in enumerate(retrieval_results):
            doc_id = f"doc_{j}"
            
            # 检查这个检索结果是否与ground truth重叠
            result_start = result.get('start_index', 0)
            result_end = result.get('end_index', 0)
            
            relevance = 0  # 默认不相关
            
            for gt in ground_truth:
                gt_start = gt.get('start_index', 0)
                gt_end = gt.get('end_index', 0)
                
                # 计算重叠度
                overlap_start = max(result_start, gt_start)
                overlap_end = min(result_end, gt_end)
                
                if overlap_start < overlap_end:
                    overlap_length = overlap_end - overlap_start
                    gt_length = gt_end - gt_start
                    
                    # 如果重叠度超过50%，认为相关
                    if overlap_length / gt_length >= 0.5:
                        relevance = 1
                        break
            
            qrel[query_id][doc_id] = relevance
        
        # 构建run：检索结果排序
        run[query_id] = {}
        
        for j, result in enumerate(retrieval_results):
            doc_id = f"doc_{j}"
            # 使用检索分数（如果有）或者使用排序位置
            score = result.get('score', 1.0 / (j + 1))  # 排序越靠前分数越高
            run[query_id][doc_id] = score
    
    print(f"✅ 转换完成: {len(qrel)} 个查询, 平均 {np.mean([len(docs) for docs in qrel.values()]):.1f} 个文档")
    return qrel, run

def calculate_traditional_metrics(qrel: Dict, run: Dict):
    """计算传统IR指标"""
    
    print(f"📊 计算传统IR指标...")
    
    try:
        import pytrec_eval
        
        # 创建评估器
        evaluator = pytrec_eval.RelevanceEvaluator(
            qrel, 
            {'map', 'ndcg', 'P_5', 'P_10', 'recall_5', 'recall_10', 'recip_rank'}
        )
        
        # 运行评估
        results = evaluator.evaluate(run)
        
        return results
        
    except Exception as e:
        print(f"❌ pytrec_eval评估失败: {e}")
        return None

def calculate_manual_metrics(qrel: Dict, run: Dict):
    """手动计算IR指标（不依赖pytrec_eval）"""
    
    print(f"📊 手动计算IR指标...")
    
    all_metrics = {
        'precision_at_5': [],
        'precision_at_10': [],
        'recall_at_5': [],
        'recall_at_10': [],
        'mrr': [],
        'map': []
    }
    
    for query_id in qrel.keys():
        if query_id not in run:
            continue
        
        # 获取相关文档
        relevant_docs = set([doc_id for doc_id, rel in qrel[query_id].items() if rel > 0])
        
        # 按分数排序检索结果
        sorted_results = sorted(run[query_id].items(), key=lambda x: x[1], reverse=True)
        
        # 计算Precision@K
        for k in [5, 10]:
            if len(sorted_results) >= k:
                top_k = [doc_id for doc_id, score in sorted_results[:k]]
                relevant_in_top_k = len(set(top_k) & relevant_docs)
                precision_at_k = relevant_in_top_k / k
                all_metrics[f'precision_at_{k}'].append(precision_at_k)
        
        # 计算Recall@K
        if len(relevant_docs) > 0:
            for k in [5, 10]:
                if len(sorted_results) >= k:
                    top_k = [doc_id for doc_id, score in sorted_results[:k]]
                    relevant_in_top_k = len(set(top_k) & relevant_docs)
                    recall_at_k = relevant_in_top_k / len(relevant_docs)
                    all_metrics[f'recall_at_{k}'].append(recall_at_k)
        
        # 计算MRR (Mean Reciprocal Rank)
        for i, (doc_id, score) in enumerate(sorted_results):
            if doc_id in relevant_docs:
                mrr = 1.0 / (i + 1)
                all_metrics['mrr'].append(mrr)
                break
        else:
            all_metrics['mrr'].append(0.0)
        
        # 计算AP (Average Precision)
        if len(relevant_docs) > 0:
            ap = 0.0
            relevant_found = 0
            
            for i, (doc_id, score) in enumerate(sorted_results):
                if doc_id in relevant_docs:
                    relevant_found += 1
                    precision_at_i = relevant_found / (i + 1)
                    ap += precision_at_i
            
            ap = ap / len(relevant_docs)
            all_metrics['map'].append(ap)
    
    # 计算平均值
    averaged_metrics = {}
    for metric_name, values in all_metrics.items():
        if values:
            averaged_metrics[metric_name] = np.mean(values)
        else:
            averaged_metrics[metric_name] = 0.0
    
    return averaged_metrics

def analyze_ir_results(results: Dict):
    """分析IR评估结果"""
    
    print(f"\n📊 传统IR评估结果")
    print("="*80)
    
    if not results:
        print("❌ 没有结果可分析")
        return
    
    # 如果是pytrec_eval的结果格式
    if isinstance(list(results.values())[0], dict):
        # 计算平均值
        all_metrics = defaultdict(list)
        for query_results in results.values():
            for metric, value in query_results.items():
                all_metrics[metric].append(value)
        
        print(f"📈 平均指标:")
        for metric, values in all_metrics.items():
            avg_value = np.mean(values)
            print(f"   {metric}: {avg_value:.4f}")
    
    # 如果是手动计算的结果格式
    else:
        print(f"📈 评估指标:")
        for metric, value in results.items():
            print(f"   {metric}: {value:.4f}")

def compare_with_deepeval():
    """与DeepEval方法对比"""
    
    print(f"\n🔄 评估方法对比")
    print("="*80)
    
    comparison = {
        "传统IR评估": {
            "优势": [
                "无需API调用，完全离线",
                "计算速度快",
                "标准化指标，易于对比",
                "成本为零"
            ],
            "指标": [
                "Precision@K: 前K个结果的精确率",
                "Recall@K: 前K个结果的召回率",
                "MAP: 平均精确率",
                "MRR: 平均倒数排名",
                "NDCG: 归一化折扣累积增益"
            ],
            "适用场景": "快速评估、批量对比、成本敏感"
        },
        "DeepEval评估": {
            "优势": [
                "语义理解能力强",
                "业界标准框架",
                "详细的评估原因",
                "支持复杂场景"
            ],
            "指标": [
                "Contextual Precision: 语义排序质量",
                "Contextual Recall: 信息完整性",
                "Contextual Relevancy: 语义相关性"
            ],
            "适用场景": "深度分析、语义评估、研究发布"
        }
    }
    
    for method, details in comparison.items():
        print(f"\n📊 {method}:")
        print(f"   优势: {', '.join(details['优势'])}")
        print(f"   适用: {details['适用场景']}")

def main():
    """主函数"""
    
    print("🎯 使用传统IR框架评估检索系统")
    print("="*100)
    
    # 检查pytrec_eval
    if not setup_pytrec_eval():
        print("📦 安装pytrec_eval...")
        if install_pytrec_eval():
            print("✅ 请重新运行脚本")
        return
    
    # 转换数据格式
    results_file = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if not Path(results_file).exists():
        print(f"❌ 未找到结果文件: {results_file}")
        return
    
    qrel, run = convert_to_trec_format(results_file)
    
    if not qrel or not run:
        print("❌ 数据转换失败")
        return
    
    print(f"\n✅ 数据转换成功!")
    print(f"📋 优势:")
    print(f"   - 无需API调用")
    print(f"   - 计算速度快")
    print(f"   - 标准化指标")
    print(f"   - 零成本评估")
    
    # 尝试使用pytrec_eval
    pytrec_results = calculate_traditional_metrics(qrel, run)
    
    if pytrec_results:
        print(f"\n🎯 使用pytrec_eval评估:")
        analyze_ir_results(pytrec_results)
    else:
        print(f"\n🎯 使用手动计算:")
        manual_results = calculate_manual_metrics(qrel, run)
        analyze_ir_results(manual_results)
        
        # 保存结果
        output_file = "traditional_ir_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(manual_results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {output_file}")
    
    # 方法对比
    compare_with_deepeval()
    
    print(f"\n🎉 评估完成!")
    print(f"\n📋 建议:")
    print(f"   ✅ 传统IR指标适合快速评估和对比")
    print(f"   ✅ 可以与DeepEval结果互补使用")
    print(f"   ✅ 建议建立多维度评估体系")

if __name__ == "__main__":
    main()
