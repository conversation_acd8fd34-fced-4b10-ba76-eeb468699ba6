#!/usr/bin/env python3
"""
测试修复后的评估指标计算

这个脚本用于验证IoU计算修复后的效果，并提供详细的对比分析。
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.run_chinese_evaluation import ChineseEvaluation
from chunking_evaluation.chunking import FixedTokenChunker
from chunking_evaluation.utils import get_openai_embedding_function

def test_single_chunker():
    """测试单个分块器以验证修复效果"""
    
    print("🔧 测试修复后的评估指标计算")
    print("="*60)
    
    # 配置路径
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    test_db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/test_fixed_db"
    
    # 检查数据集是否存在
    if not os.path.exists(questions_csv_path):
        print(f"❌ 错误：找不到中文数据集文件: {questions_csv_path}")
        return None
    
    # 创建评估器
    evaluator = ChineseEvaluation(questions_csv_path, test_db_path)
    
    # 创建一个小的分块器进行测试
    chunker = FixedTokenChunker(chunk_size=200, chunk_overlap=20)
    
    # 获取嵌入函数
    embedding_function = get_openai_embedding_function()
    
    print(f"📊 测试配置:")
    print(f"   分块器: FixedTokenChunker(chunk_size=200, chunk_overlap=20)")
    print(f"   数据库: {test_db_path}")
    print(f"   检索数量: 5")
    
    try:
        # 运行评估
        result = evaluator.run(
            chunker=chunker,
            embedding_function=embedding_function,
            retrieve=5,
            db_to_save_chunks=test_db_path,
            include_debug_info=True
        )
        
        print(f"\n✅ 评估完成!")
        print(f"📈 修复后的结果:")
        print(f"   精确率: {result.get('precision_mean', 0):.4f}")
        print(f"   召回率: {result.get('recall_mean', 0):.4f}")
        print(f"   IoU分数: {result.get('iou_mean', 0):.4f}")
        
        # 检查IoU是否仍等于精确率
        precision_mean = result.get('precision_mean', 0)
        iou_mean = result.get('iou_mean', 0)
        
        if abs(precision_mean - iou_mean) < 0.0001:
            print(f"⚠️  警告: IoU仍然等于精确率，可能修复未生效")
        else:
            print(f"✅ 修复成功: IoU与精确率不再相等")
        
        # 分析前几个问题的详细结果
        if 'debug_info' in result and 'questions' in result['debug_info']:
            print(f"\n🔍 前3个问题的详细分析:")
            questions = result['debug_info']['questions'][:3]
            
            for i, q in enumerate(questions):
                metrics = q.get('metrics', {})
                print(f"\n   问题 {i+1}: {q.get('question_text', '')[:50]}...")
                print(f"     精确率: {metrics.get('precision_score', 0):.4f}")
                print(f"     召回率: {metrics.get('recall_score', 0):.4f}")
                print(f"     IoU分数: {metrics.get('iou_score', 0):.4f}")
                print(f"     检索分块数: {metrics.get('chunks_retrieved', 0)}")
        
        return result
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_with_old_results():
    """与旧结果进行对比"""
    
    print(f"\n📊 与修复前结果对比:")
    print("="*60)
    
    # 读取旧的结果文件
    old_results_path = "chunking_evaluation/evaluation_framework/outputs/chinese_evaluation_results.json"
    
    if os.path.exists(old_results_path):
        with open(old_results_path, 'r', encoding='utf-8') as f:
            old_results = json.load(f)
        
        # 查找FixedTokenChunker_chunk_overlap20_chunk_size200的结果
        old_config_key = "FixedTokenChunker_chunk_overlap20_chunk_size200"
        if old_config_key in old_results:
            old_questions = old_results[old_config_key].get('questions', [])
            
            if old_questions:
                print(f"📋 修复前的结果 (前3个问题):")
                for i, q in enumerate(old_questions[:3]):
                    metrics = q.get('metrics', {})
                    print(f"   问题 {i+1}:")
                    print(f"     精确率: {metrics.get('precision_score', 0):.4f}")
                    print(f"     召回率: {metrics.get('recall_score', 0):.4f}")
                    print(f"     IoU分数: {metrics.get('iou_score', 0):.4f}")
                    
                    # 验证IoU是否等于精确率
                    precision = metrics.get('precision_score', 0)
                    iou = metrics.get('iou_score', 0)
                    if abs(precision - iou) < 0.0001:
                        print(f"     ⚠️  IoU = 精确率 (修复前的错误)")
            else:
                print("❌ 未找到旧结果中的问题数据")
        else:
            print(f"❌ 未找到配置 {old_config_key} 的旧结果")
    else:
        print(f"❌ 未找到旧结果文件: {old_results_path}")

def main():
    """主函数"""
    
    print("🎯 测试修复后的评估指标")
    print("="*80)
    
    # 测试修复后的效果
    new_result = test_single_chunker()
    
    if new_result:
        # 与旧结果对比
        compare_with_old_results()
        
        print(f"\n🎉 测试完成!")
        print(f"\n💡 修复说明:")
        print(f"   - 修复了IoU计算公式中的错误")
        print(f"   - IoU现在正确计算为: 交集 / (检索分块 + 参考答案 - 交集)")
        print(f"   - 添加了除零保护")
        print(f"   - IoU应该不再等于精确率")
    else:
        print(f"\n❌ 测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
